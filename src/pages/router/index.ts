// @ts-nocheck
/**
 * 点单router
 * 本页面主要负责收钱吧点单页面路由跳转(微信)
 * 久久不回使用该页面
 *
 * 收钱吧对应对页面如下:
 * -. page-home         点单首页      p0o2sfaiwk21
 * - page-search       搜索商品页    p0268od3h4gy
 * - page-submit       轻餐提交订单   886iep4iu37w
 * - page-remark       轻餐提交订单   l9tw9m8vgo2d
 * - page-address      输入用户地址页  u0w1f83ptdyv
 * - page-my-order     我的订单       71r5mxag59at
 * - page-order-detail 订单详情       8f4uj9ixh1c1
 * - page-order-pay    订单支付       nhsvs86ckd8l
 * - page-result       订单详情       9538qq4mis40
 * - page-cart         围餐-下订单    19771xt9o39d
 * - page-user-count   围餐-选择人数   ozsamyi986g2
 * - page-submit-order 围餐-确认下单   4ky3lsc29l30
 * - page-closing      空态页         hl7kiuilhx5t
 * - page-customer-pay 订单结算       zrhi84mchgov
 * - page-combo        套餐详情页     6w1p4xnwqf5p
 * - page-aggregation  聚合码店铺列表页 h6g14w38mf22
 * - page-router       扫码落地页     s11gfxefx0j6
 * - page-aggregation-list 聚合列表页
 * - page-campus       校园外卖      9q25ti3259cn
 * - page-order-map    地图         xa5wi02ur2vb
 */

import _ from '@wosai/emenu-mini-lodash'
import { ECHO, shouldUseCache, StorageUtils, getGlobal } from '@wosai/emenu-mini-utils'
// import { isWeixin } from '@wosai/emenu-mini-utils' // 已注释，仅用于广告预加载
import qs from 'qs'
import CONFIG from '@wosai/emenu-mini-config'
import { TLogin, TPayload } from '@types'
import { loginByWid } from '@utils'
import { redirect } from './router.utils'
import { useThemeBehavior } from '@behaviors'
import { apolloInit } from '@utils/apollo'
import BaseComponent from '@BaseComponent'

// #ifdef wechat
import mpbBehaviorOpenqrpay from '@sqb/mp-behavior-openqrpay'
// #endif
// #ifdef alipay
import mpbBehaviorOpenqrpay from '@sqb/mp-behavior-openqrpay/alipay/legacyMixin'
// #endif

const { APP_LAUNCH_AT_KEY } = CONFIG
const platform = typeof wx !== 'undefined' ? wx : my
const LANGUAGE_KEY = 'smart:language'
const LANGUAGE_TTL = 5 * 60
// 支持跳转至收钱吧付款页面
const { storage } = StorageUtils
// 检测预加载数据是否过期
const EXPIRE_TIME = 1000 * 60 * 2 // 2分钟

let isLoginSucceed = false
const isExpiredPreloadData = scancodeTime => Date.now() - scancodeTime > EXPIRE_TIME

export const options = {
  // behaviors: [mpbBehaviorOpenqrpay],
  behaviors: [mpbBehaviorOpenqrpay, useThemeBehavior([])],
  properties: {
    query: {
      type: Object,
      value: {}
    },
    config: {
      type: Object,
      value: {}
    }
  },
  data: {
    componentName: 'page-router',
    isReady: false,
    // 主要解决是当前页为home时，页面不跳转， query为空
    // 如果是商品海报，则goodsId不在query
    homeQuery: null
  },
  lifetimes: {
    created() {
      if (global && global.pageSet) {
        this.__key = 'routerComp'
        global.pageSet.add(this)
      }
    },
    attached() {
      console.log('@router attached', Date.now())
      const { sqbBridge } = this.data
      if (platform[APP_LAUNCH_AT_KEY]) platform[APP_LAUNCH_AT_KEY] = Date.now()

      // 保存绑定的事件处理函数引用
      this._boundOnShow = this.onShow.bind(this)

      // 判断小程序是否为商家小程序
      // 以后
      try {
        const { platform } = sqbBridge.getContext()
        const isMerchant = _.get(platform, 'type') === 'merchant'
        storage('emenu:isMerchant', isMerchant)
      } catch (error) {}

      const now = Date.now()
      platform['_emenu_now'] = now

      // const duration = Date.now() - platform[APP_LAUNCH_AT_KEY];
      // sqbBridge.sls('PERFORMANCE', { type: 'page-router-created', duration, startAt: Date.now() });

      this.call('setNavigation', {
        custom: true,
        homeButton: false
      })
      this.clearStoreInfo()

      let appId
      if (_.isFunction(sqbBridge.getContext)) {
        const context = sqbBridge.getContext()
        appId = _.get(context, 'platform.appId')
      }
      /**************************处理预拉取数据******************************/
      const handlePrefetch = prefetchedData => {
        let preFetchData: { data: TPayload } = _.get(prefetchedData, 'fetchedData.mk')
        if (!preFetchData) {
          const _query = sqbBridge.getMiniProgramQuery()
          const { storeId, serviceTypeName = CONFIG.SUBSCRIBE_ORDER, q } = _query || {}
          if (!q && storeId && serviceTypeName) {
            // @ts-ignore

            const cachedPayload = storage(`emenu:${storeId}_${serviceTypeName}`) || {}
            if (shouldUseCache(cachedPayload)) {
              preFetchData = cachedPayload
            }
          }
        }
        if (preFetchData) {
          const duration = Date.now() - platform[APP_LAUNCH_AT_KEY]
          sqbBridge.sls('PERFORMANCE', {
            type: 'page-router-pre-fetch-end',
            startAt: Date.now(),
            duration,
            description: '表示有预拉取数据'
          })
          const { data } = preFetchData
          // @ts-ignore
          const prefetchQuery: { q: string; scancode_time: string } = qs.parse(prefetchedData.query)
          const { q, scancode_time } = prefetchQuery || {}

          if (scancode_time) {
            const scancodeTime = Number(scancode_time) * 1000
            // 预加载数据是否失效
            if (!isExpiredPreloadData(scancodeTime)) {
              ECHO('#GATHER - initByPayload from router - 预拉取数据')
              sqbBridge.initByPayload(data, decodeURIComponent(q))
            }

            // 用户是否有已经登录
            const user = _.get(data, 'login', {}) as TLogin

            return { isLogin: !isExpiredPreloadData(scancodeTime) && user.token, user }
          } else {
            const storeId = _.get(data, 'store.storeId')
            ECHO('#GATHER - initByPayload from router - 手动预拉取')
            storeId && sqbBridge.initByPayload(data, storeId)
            const user = _.get(data, 'login', {}) as TLogin
            return { isLogin: user && user.token, user }
          }
        }
        return { isLogin: false }
      }
      /**************************处理是否登录******************************/
      const handleLogin = async ({ isLogin, user }: { isLogin: boolean; user: TLogin }) => {
        const hillsStage = sqbBridge.getHillsStage()
        let retriedTimes = 0
        const retryTimes = 5
        const retryTimeout = 100
        let timerId

        const loginFn = async () => {
          const { token, openid, unionid } = await this.getUserInfo().catch(err => {
            sqbBridge.sls('INFO', {
              type: 'sqbGetUserInfo',
              data: { appId, token, openid, unionid, err }
            })
            return {}
          })

          // const header = buildHeader(token, hillsStage);

          if (isLogin && user) {
            clearTimeout(timerId)
            return { data: { data: user }, isLogin }
          } else {
            // 重试次数完成或者已经登录成功
            // @ts-ignore
            if (isLoginSucceed || retriedTimes === retryTimes)
              return { data: { data: storage('LOGIN') } }

            // const loginUrl = '/mk/v1/user/login';
            // 存储收钱吧JWT
            // 登录智慧经营业务系统
            ECHO('[E-MENU][PERF] - 登录开始', Date.now() - now)
            // const data = {
            //   appId,
            //   thirdPartUserId: openid,
            //   wxUnionId: unionid,
            // };

            // const userResp = await sqbBridge
            //   .request({
            //     url: loginUrl,
            //     method: 'POST',
            //     header,
            //     data,
            //   })
            //   .then(handleLoginData)
            //   .catch(() => null);

            const userResp = await loginByWid(sqbBridge)
              .then(res => handleLoginData({ isLogin: void 0, data: { data: res } }))
              .catch(() => null)

            ECHO('重试登录', { retriedTimes })
            retriedTimes += 1
            clearTimeout(timerId)
            timerId = setTimeout(loginFn, retryTimeout)

            return userResp
          }
        }

        return await loginFn()
      }
      /**************************保存登录数据 ******************************/
      const handleLoginData = (userResp: { isLogin: boolean; data: { data: TLogin } }) => {
        if (!userResp) throw new Error('登录失败')

        if (!userResp.isLogin) {
          const duration = Date.now() - platform[APP_LAUNCH_AT_KEY]
          sqbBridge.sls('PERFORMANCE', {
            type: 'page-router-login-end',
            duration,
            startAt: Date.now(),
            description: '登录结束'
          })
        }
        const {
          data: { data }
        } = userResp

        isLoginSucceed = true
        // storage('LOGIN', data);
        // storage('currentUser', data);
        // if (data && data.token) {
        //   storage('token', data.token);
        // }
        // this.setData({ isReady: true });
        return data
      }
      /**************************处理跳转 ******************************/
      const handleRedirect = async () => {
        const { sqbBridge } = this.data
        const { openid, uc_user_id } = await this.getUserInfo().catch(err => {
          sqbBridge.sls('INFO', {
            type: 'sqbGetUserInfo',
            data: { openid, uc_user_id, appId, err }
          })
          return {}
        })

        //注意 getMiniProgramQuery 一定要放在函数内， 否则在created无法获取query参数
        const query = sqbBridge.getMiniProgramQuery()

        const { _terminalTags, ...restQuery } = query || {}

        // 收银音箱二维码标签
        if (_terminalTags) {
          try {
            const terminalTagValue = _.get(
              JSON.parse(decodeURIComponent(_terminalTags)),
              'TAG_CASHIER'
            )
            if (terminalTagValue) {
              _.assign(restQuery, { terminalTagValue })
              const globalData = getGlobal()
              _.set(globalData, 'smart.terminalTagValue', terminalTagValue)
            }
          } catch (error) {
            console.log(error)
          }
        }

        const { storeId = '', language = '' } = restQuery
        if (storeId && language) {
          const _query = _.omit(restQuery, ['language'])
          const pages = getCurrentPages()
          const currentPage = pages[pages.length - 1]
          const currentPagePath = currentPage.route
          storage(LANGUAGE_KEY, { language, timestamp: Date.now() }, LANGUAGE_TTL)
          wx.reLaunch({
            url: `/${currentPagePath}?${qs.stringify(_query)}`
          })
        }
        /**************************处理广告******************************/

        const duration = Date.now() - platform[APP_LAUNCH_AT_KEY]
        sqbBridge.sls('PERFORMANCE', {
          type: 'page-router-redirect-start',
          startAt: Date.now(),
          duration,
          description: '开始处理跳转逻辑'
        })

        this.bridge = sqbBridge

        await redirect.bind(this)({ ...restQuery, openid, appid: appId, loginUserId: uc_user_id })
      }

      sqbBridge
        .getBackgroundFetchData({
          fetchType: 'pre',
          json: true
        })
        .then(handlePrefetch)
        .catch(() => {
          return { isLogin: false }
        })
        .then(handleLogin)
        .then(apolloInit)
        .then(handleRedirect)
        // .then(handleAd)
        .catch((err: Error) => {
          console.error('login error', err)
        })
      // 监听页面show事件
      this.call('on', 'show', this._boundOnShow)
    },
    detached() {
      // 解除事件监听
      this.call('off', 'show', this._boundOnShow)

      // 清除绑定函数的引用以防止内存泄漏
      this._boundOnShow = null
      this.bridge = null
    }
  },
  methods: {
    onShow() {
      // const { sqbBridge } = this.data
      // 广告处理逻辑已注释 - 暂时不需要使用
      // const handleAd = () => {
      //   const store = sqbBridge.getStoreInfo()
      //   const timerId = setTimeout(() => {
      //     const context = sqbBridge.getContext && sqbBridge.getContext()
      //     if (_.get(context, 'platform.type') !== 'merchant') {
      //       // 商家小程序无广告
      //       //  setAdsPreRender(this, store); 删除预加载广告，那边出现bug
      //       clearTimeout(timerId)
      //     }
      //   }, 0)
      //   // 广告数据预加载 - 已注释，暂时不需要使用
      //   // if (isWeixin()) {
      //   //   try {
      //   //     // @ts-ignore
      //   //     wx.preloadAd([
      //   //       {
      //   //         unitId: 'adunit-84999453a1fc2940', // banner广告广告单元
      //   //         type: 'banner' // banner广告
      //   //       }
      //   //     ])
      //   //   } catch (error) {
      //   //     console.log(error)
      //   //   }
      //   // }
      // }
      // handleAd()
    },
    clearStoreInfo() {
      const { sqbBridge } = this.data
      const query = sqbBridge.getMiniProgramQuery() || {}
      // @ts-ignore
      const storeInfo = storage('storeInfo') || {}
      const curStoreId = query.storeId
      const originStoreId = storeInfo.storeId

      if (curStoreId !== originStoreId) {
        // @ts-ignore
        storage({ terminalInfo: null, storeInfo: null, mcc: null })
      }
    },
    getScene() {
      const { sqbBridge } = this.data
      const extra = sqbBridge.getExtra() || {}
      let { scene } = extra
      const isCodeType = /^[a-z]{1}$/.test(scene)
      scene = isCodeType ? `qrcode_${scene}` : scene || 'manual'
      // @ts-ignore
      storage({ scene })
      return scene
    },
    getUserInfo(): Promise<{ token: string; uc_user_id: string; openid: string; unionid: string }> {
      const { sqbBridge } = this.data
      const loginInfo = sqbBridge.getLoginInfo() || {}
      // TODO:
      // getLoginInfo和getPlatformLoginInfo依赖identify
      // 所以这两个方法返回值有可能为空
      // 如果值为空， 则设置setTimeout获取值
      return new Promise((resolve, reject) => {
        if (_.isFunction(sqbBridge.getPlatformLoginInfo)) {
          const platformLoginInfo = sqbBridge.getPlatformLoginInfo()
          loginInfo.openid =
            _.get(platformLoginInfo, 'openid') || _.get(platformLoginInfo, 'user_id')
          loginInfo.unionid = _.get(platformLoginInfo, 'unionid')
        }
        if (!loginInfo.openid) {
          reject('当前获取openid为空')
        }
        ECHO('#getLoginInfo', { loginInfo })
        resolve(loginInfo)
      })
    }
  }
}

BaseComponent(options)
