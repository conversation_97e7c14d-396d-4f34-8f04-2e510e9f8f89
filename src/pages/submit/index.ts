import * as UTILS from '@utils'
import {
  emenuChannel,
  PhoneVerify,
  toCurrency,
  requestPolicyWithBridge,
  isPrivacyAuthorized,
  setPrivacyAuthStatus
} from '@utils'
import { onSwitchLogin } from '@utils/userSwitch'
import CONFIG from '@wosai/emenu-mini-config'
import { aliAuthGetPhone, checkSession, useTrack } from '@utils/helper'
import _ from '@wosai/emenu-mini-lodash'
import {
  ECHO,
  FunctionUtils,
  getMpScene,
  getPayway,
  getTerminalName,
  isAlipay,
  isWeixin,
  ObjectUtils,
  StorageUtils
} from '@wosai/emenu-mini-utils'
import SodexoPaySDK from '@sqb/sdk-pay-sodexo-card'
import SubmitManager from './SubmitManager'
import { useThemeBehavior } from '@behaviors'
// __WX
// @ts-ignore
/* #if name == 'wechat' */
import payChannel from '@sqb/mp-pay-plugin-direct/payChannel'
/* #endif */
/* #if name == 'alipay' */
import payChannel from '@sqb/mp-pay-plugin-direct/es/payChannel'
/* #endif */
import { updateMkCustomInfoByDiscount } from '@utils/discount'
import SQBADVERTISING from '@sqb/ads-preload' // 已注释，暂时不需要使用广告预加载
import BaseComponent from '@BaseComponent'
import { AlertBehavior, useI18nBehavior } from '@behaviors'
import {
  TCamelAddress,
  TFetchExtraParams,
  TFetchPayloadParams,
  TPayway,
  TServiceTypeName
} from '@types'
import {
  pageSubmitKeys,
  componentDeliverTabKeys,
  componentAddressSheetKeys,
  componentTimeSheetKeys,
  componentPriceInfoKeys,
  componentMealTypeKeys,
  componentUserCountDialogKeys,
  componentgoodsSubsidyKeys,
  componentSubmitGoodsDetail
} from '@utils/i18n_keys'

const { AOP_WX_SUBMIT_FLOAT_CODE, AOP_MY_SUBMIT_FLOAT_CODE, MCC_DELIVERY_TYPE, MCDONALDS_STORE } =
  CONFIG
const CONSTANT = CONFIG
const { storage } = StorageUtils
const { camelCaseKeys, snakeCaseKeys } = ObjectUtils
const { delay } = FunctionUtils
/*
 将优惠返回以下字段传入预下单接口
 这只是临时方案， 这个方案由于网络异步的问题， 可能会导致传入错误的参数
 导致退款时会有问题
 未来， 统一收银台会解决该问题
*/
let extraPayParams = {
  redeem_digest: null,
  total_discount: null
}

export const MEAL_TYPES: TSubmit.TSubmitPayload['mealTypes'] = [
  {
    name: '堂食',
    disabledName: '不支持堂食',
    icon: 'https://smart-static.wosaimg.com/99zhe/order/tangshi.png',
    icon_selected: 'https://smart-static.wosaimg.com/emenu/dine_in_selected.png',
    value: 'store',
    disabled: false
  },
  {
    name: '打包',
    disabledName: '不支持打包',
    icon: 'https://smart-static.wosaimg.com/99zhe/order/ziqu.png',
    icon_selected: 'https://smart-static.wosaimg.com/emenu/pack_selected.png',
    value: 'takeout',
    disabled: false
  }
]

//   GET_CARTS_ERROR("E0101", "获取购物车失败"),
//   GET_PACK_AMOUNT_ERROR("E0102", "计算打包费失败"),
//   GET_BOOK_TIMES_ERROR("E0103", "获取预定时间失败"),
//   GET_CAMPUS_ERROR("E0104", "获取门店入驻校园信息失败"),
//   GET_AGREEMENT_ERROR("E0105", "获取签署外卖协议失败"),
//   GET_ALL_ADDRESS_ERROR("E0106", "获取所有地址失败"),
//   GET_DEFAULT_ADDRESS_ERROR("E0107", "获取默认地址失败"),
//   GET_DELIVERY_AMOUNT_ERROR("E0108", "计算配送费失败"),
//   GET_DISCOUNTS_AMOUNT_ERROR("E0109", "计算订单优惠失败"),

const CARTS_ERROR_CODE = 'E0101'
const PACK_AMOUNT_ERROR_CODE = 'E0102'
const BOOK_TIMES_ERROR_CODE = 'E0103'
// 配送费
const DELIVERY_FEE_ERROR_CODE = 'E0108'
// 不阻断下单流程错误码
const BLOCK_ERROR_CODES = [CARTS_ERROR_CODE, PACK_AMOUNT_ERROR_CODE, BOOK_TIMES_ERROR_CODE] as const
const USER_DELIVERY_DATA = [
  {
    id: 'upstairs',
    icon: 'deliveryUpstairsIcon',
    name: '配送上楼',
    floor: '',
    fee_desc: '足不出户享用美食',
    currentFloor: '',
    lastFloor: '',
    status: false,
    floorIndex: -1
  },
  {
    id: 'downstairs',
    icon: 'deliveryDownstairsIcon',
    name: '配送到楼下',
    floor: '',
    fee_desc: '宿舍楼下自取',
    currentFloor: 0,
    status: false
  }
]

// 组件创建时间
let componentCreateAt = 0

function getPaymentScene(
  serviceTypeName: TServiceTypeName,
  takeoutProfitSharing = false,
  supportCardPay
): 'smart_wm' | 'smart_store' {
  // const takeoutProfitSharing = await this.bridge.getMcc(CONSTANT.MCC_TAKEOUT_PROFIT_SHARING);
  let needShowStoredPay
  if (supportCardPay) {
    needShowStoredPay = supportCardPay[serviceTypeName]
  } else {
    needShowStoredPay = !(takeoutProfitSharing && serviceTypeName === 'take_out_order')
  }

  // if (!needShowStoredPay) return 'smart_wm';
  return 'smart_store'
  // const serviceTypeNamesMaps = {
  //   take_out_order: 'smart_wm',
  //   subscribe_order: 'subscribe_order',
  //   pre_order: 'pre_order',
  //   EAT_FIRST_ORDER: 'EAT_FIRST_ORDER',
  // };
  // return serviceTypeNamesMaps[serviceTypeName];
}

const floatingBubblePic = 'https://smart-static.wosaimg.com/emenu/campus_group_float.gif'

BaseComponent({
  options: {
    multipleSlots: true
  },
  behaviors: [
    AlertBehavior(),
    useI18nBehavior({
      keys: [
        ...pageSubmitKeys(),
        ...componentDeliverTabKeys(),
        ...componentAddressSheetKeys(),
        ...componentTimeSheetKeys(),
        ...componentPriceInfoKeys(),
        ...componentMealTypeKeys(),
        ...componentUserCountDialogKeys(),
        ...componentgoodsSubsidyKeys(),
        ...componentSubmitGoodsDetail()
      ],
      componentId: 'smart-i18n-provider'
    }),
    useThemeBehavior([], 'submit-page')
  ],
  properties: {
    config: {
      type: Object,
      value: {}
    }
  },
  data: {
    supportCardPay: null,
    takeoutProfitSharing: false,
    // wap_pay_request: null,
    wechatPayLuginThemeName: 'classic',
    cashierBizParams: null,
    cashierPayTools: [],
    // @ts-ignore
    useCashier: true,
    payChannel,
    componentName: 'page-submit',
    errors: [],
    addressCode: '', // 结构化地址
    addressList: [],
    addressName: '',
    adsense: {}, // 运营位数据
    aliShouldAuthPhone: false,
    bannerTrackParams: { placement: '提交订单页' }, // banner 埋点数据
    bookTimes: [],
    campusType: 0,
    cancelAuthPhoneNumber: false,
    canEditPeopleNum: false,
    cart: null,
    cellphone: null,
    currentAddressId: null,
    currentTime: '',
    defaultAddress: <TCamelAddress>{},
    devCode: CONSTANT.PLUGIN_HOME_PATH,
    disabledStored: '',
    discount: null,
    goodsCouponCount: 0, // 商品抵用券数量
    goodsSubsidyInfo: null, // 微信加价购参数
    hasAgreement: true, // 是否签署过外卖协议， 堂食后端返回true
    hbfqModalRef: null,
    huabeiParams: [],
    invalidGoodsList: [],
    isDamBoard: false,
    isInCampus: false,
    isNativePay: true, // 是否使用微信&支付宝原生支付
    isNeedStoredRecharge: false, // 支付需要储值充值
    isPayingStoredCard: false, // 是否是正在购买储值卡
    isShowAlipayModal: false,
    isShowPriceChange: false,
    isShowSelectTip: false,
    isShowStoredCard: false,
    isTakeOutOrder: false,
    isWeixin: isWeixin(),
    loaded: false, // 初始化数据是否加载完成
    loveDonateOptions: null,
    mealTypes: MEAL_TYPES,
    orderno: null,
    originalPrice: 0, // 优惠前支付总金额（除配送费）
    originCart: null,
    packAmount: 0,
    packed: false,
    paying: false,
    paytype: getTerminalName(), // payway
    payway: getPayway(),
    phoneNumber: null,
    PRE_ORDER: CONSTANT.PRE_ORDER,
    RETAIL_ORDER: CONSTANT.RETAIL_ORDER,
    presetTimeDict: {},
    realDeliveryFee: null,
    requirePhone: false, // 预留手机号 是否必填
    selectAddressSheetShow: false,
    selectTimeSheetShow: false,
    serviceType: 2,
    serviceTypeName: '',
    showMkssStoredActivityInSubmit: false, // 是否显示储值支付
    showPhoneNumberDialog: false,
    showSodexoPay: false, // 是否显示索迪斯支付
    showStoredPay: false,
    sign: false,
    storeConfigs: null,
    SUBSCRIBE_ORDER: CONSTANT.SUBSCRIBE_ORDER,
    supportCampusDelivery: false,
    tabBarData: [],
    TAKE_OUT_ORDER: CONSTANT.TAKE_OUT_ORDER,
    terminal: null,
    timerIdexMap: {
      [CONSTANT.PRE_ORDER]: {
        dayIndex: 0,
        timeIndex: -1
      },
      [CONSTANT.TAKE_OUT_ORDER]: {
        dayIndex: 0,
        timeIndex: -1
      }
    },
    // 浮窗AOP数据
    floatingBubbleData: {},
    floatingBubbleVisible: true,
    isFetchingDiscount: false,
    // 用户配送上楼模块数据
    userDeliveryData: USER_DELIVERY_DATA,
    deliveryFloorData: [],
    currentFloor: null,
    // @ts-ignore
    userLastChange: null,
    wrapperStyle: { background: '#fff' },
    _is_zero_pay: false,
    IS_ENABLE_ZERO_PAY: true,
    emenuChannel,
    // 是否显示会员积分抵现弹窗
    isShowPointsPopup: false,
    // 查优惠接口返回的 mk_custom_info 内容
    pointsRedeemDetail: null,
    // 会员积分值
    memberPointsValue: 0,
    bulletinPayload: {}, // 传给合规提示栏的参数
    // 是否为麦当劳店铺
    isMcDonaldStore: false,
    // 是否是零售店铺
    isRetail: false
  },
  td: {
    onPay: {
      type: 'throttle'
    },

    onSubsidyAddClick: {
      type: 'throttle',
      delay: 500
    },

    onSubsidyDecClick: {
      type: 'throttle',
      delay: 500
    },

    transformMaterialData: {
      type: 'debounce',
      delay: 200
    }
  },
  observers: {
    config(val) {
      if (!val) return
      const { mealTypes } = this.data
      mealTypes.forEach(item => {
        if (item.value === 'store') {
          item.icon_style = val.mealTypeEatInIconStyle
          item.style = val.mealTypeselectedDefaultStyle
          item.style_selected = val.mealTypeselectedActStyle
        }
        if (item.value === 'takeout') {
          item.icon_style = val.mealTypeBagIconStyle
          item.style = val.mealTypeselectedDefaultStyle
          item.style_selected = val.mealTypeselectedActStyle
        }
      })
      this.setData({ mealTypes })
    }
  },
  lifetimes: {
    created() {
      this.boundBeforeSubmit = this.onBeforeSubmitEvent.bind(this)
    },
    async attached() {
      componentCreateAt = Date.now()
      const { sqbBridge, theme } = this.data
      const { storeId, merchantId, storeSn } = this.getStore()
      const { ruleId, isStoredPaySelected, goodsCouponCount, memberPoints, isRetail } =
        this.getQuery()
      const attemptIsRetail = _.attempt(JSON.parse, isRetail)
      emenuChannel.setMkCustomInfo({ member_points: memberPoints })
      wx.hideShareMenu()
      this.phoneVerify = new PhoneVerify(this, this.data.cellphone)
      // MISC
      this.call('track', 'SmMpPageDisplay')
      this.call('setNavigation', {
        backgroundColor: _.get(theme, 'settings.navigationBarColor') || '#000000',
        custom: true,
        homeButton: false,
        backColor: '#000000'
      })
      this.trackIns = useTrack(this)
      this.storeId = storeId
      this.initialMkCustomInfo = _.cloneDeep(emenuChannel.getMkCustomInfo())
      this.setData({
        ruleId,
        goodsCouponCount: ~~goodsCouponCount,
        // @ts-ignore
        floatingBubbleVisible: storage('floatingBubbleVisible'),
        memberPointsValue: memberPoints,
        isRetail: _.isError(attemptIsRetail) ? false : attemptIsRetail
      })

      if (sqbBridge.isJJZ) {
        this.setData({
          xEnvFlag: sqbBridge.getMiniProgramEnv(),
          mpEnv: sqbBridge.getPrestrainEnv()
        })
      }

      const takeoutProfitSharing = (await sqbBridge.getMcc(
        CONSTANT.MCC_TAKEOUT_PROFIT_SHARING
      )) as boolean
      const supportCardPay = sqbBridge.getSupportCardPay()
      this.setData({ takeoutProfitSharing, supportCardPay })

      if (
        _.isFunction(this.data.payChannel.registerBeforeSubmitEvent) &&
        !this.hasRegisteredBeforeSubmit
      ) {
        // aOnBeforeSubmitEvent = onBeforeSubmitEvent.call(this);
        console.log('#attached', 'registerBeforeSubmitEvent')
        this.data.payChannel.registerBeforeSubmitEvent(this.boundBeforeSubmit)
        this.hasRegisteredBeforeSubmit = true
      }

      // 页面初始化时监听统一收银台事件
      setupPayPluginListeners.call(this)
      storage('pageRemark', '')
      // THEME CONFIG
      // this.themeUI(config)
      this.serviceType = sqbBridge.getServiceTypes('serviceType')
      this.setData({
        bulletinPayload: {
          merchantId,
          storeId,
          serviceType: this.serviceType
        }
      })
      // 手动触发推荐加料
      this.resetRecommendMaterial()
      // @ts-ignore
      this.manager = SubmitManager.build({ view: this, bridge: sqbBridge })
        .tapAsync('order.before', handleOrderBefore.call(this))
        // this.manager.tapAsync('order.before', handleOrderBefore.call(this));
        .tapAsync('order.before', (order: { data: any }, cb: (arg0: null, arg1: any) => void) => {
          const { cashierBizParams, useCashier } = this.data
          ECHO('order.before', order, cb)
          const pay_way = _.get(cashierBizParams, 'usingPayTools.0.code')
          if (cashierBizParams && pay_way) {
            _.assign(order.data, {
              pay_way,
              cashier_biz_params: cashierBizParams,
              mk_custom_info: emenuChannel.getMkCustomInfo(), // 优惠核销参数
              ...extraPayParams
            })
          } else {
            _.assign(order.data, {
              mk_custom_info: emenuChannel.getMkCustomInfo(), // 优惠核销参数
              ...extraPayParams
            })
          }
          _.assign(order.data, { useCashier })

          cb(null, order)
        })
        .tapAsync(
          'order.before',
          (order: { data: { delivery_info: any } }, cb: (arg0: null, arg1: any) => void) => {
            const { currentFloor } = this.data
            _.assign(order.data.delivery_info, { floor: currentFloor })
            cb(null, order)
          }
        )
        .tapAsync('order.before', (order: { data: any }, cb: (arg0: null, arg1: any) => void) => {
          let { campusOrderStatistics } = this.getQuery()
          if (campusOrderStatistics) {
            try {
              // 两次解码防止上报给火山的数据埋点乱码
              campusOrderStatistics = JSON.parse(
                decodeURIComponent(decodeURIComponent(campusOrderStatistics))
              )
              _.assign(order.data, { client_tracking_data: campusOrderStatistics })
            } catch (err) {}
          }
          cb(null, order)
        })
        .tapAsync(
          'order.before',
          (order: { data: any }, cb: (arg0: null, arg1: any) => void): void => {
            const { _is_zero_pay } = this.data
            if (_is_zero_pay) {
              _.assign(order.data, { pay_way: getPayway() })
              this.$loading('')
            }
            _.assign(order.data, { _is_zero_pay })
            cb(null, order)
          }
        )
        .tapAsync(
          'order.before',
          (order: { data: { extra: any } }, cb: (arg0: null, arg1: any) => void) => {
            // @ts-ignore
            const { campusActivityParams, cellphone, serviceTypeName } = this.data

            // 上报手机号使用隐私场景
            if (cellphone) {
              this.reportPrivacySceneOnce(`page-submit-phone-${serviceTypeName}`)
            }

            if (!isAlipay()) {
              const groupBuyingActivity = _.get(campusActivityParams, 'groupBuyingActivity')
              const groupId = _.get(campusActivityParams, 'groupId')
              if (!_.isEmpty(groupBuyingActivity)) {
                _.assign(order.data, { group_buying_activity: snakeCaseKeys(groupBuyingActivity) })
              }

              if (groupId) {
                _.assign(order.data.extra, { group_buying_id: groupId })
              }
            }
            cb(null, order)
          }
        )
        // 解决工单 #2025040713122329
        .tapAsync('order.before', (order: { data: any }, cb: (arg0: null, arg1: any) => void) => {
          // 获取终端信息
          // @ts-ignore
          // const terminal = storage('smart-mp:terminal')
          const terminal = this.getTerminalFromStorage()
          const store = this.data.store
          const serviceTypeName = this.data.serviceTypeName

          const isTerminalValid = _.every([
            terminal,
            store?.storeId,
            terminal?.store_id === store?.storeId,
            terminal?.tableId,
            serviceTypeName == CONSTANT.SUBSCRIBE_ORDER
          ])

          if (isTerminalValid) {
            const orderTerminal = this.createTerminalForOrder(terminal)
            _.assign(order.data, orderTerminal)
          }
          sqbBridge.sls('INFO', {
            type: 'submit:order:before',
            data: { ...(order.data || {}), serviceTypeName: this.data.serviceTypeName }
          })
          cb(null, order)
        })
        .tapAsync('order.pay_before', handleOrderPayBefore.call(this))
        .tap('order.order', handleOrderOrder.call(this))
        .tap('order.success', handleOrderSuccess.call(this))
        .tap('order.fail', handleOrderFail.call(this))
        .tap('order.pay_success', handleOrderPaySuccess.call(this))
        .tap('order.pay_fail', handleOrderPayFail.call(this))
        .tap('order.pay_cancel', handleOrderPayCancel.call(this))
        .on('subscribe_order.type.change', onSubscribeOrderTypeChange.call(this))
        .on('paytype.change', onPaytypeChange.call(this))
        .on('take_out_order.type.change', onTakeoutOrderTypeChange.call(this))
        .on('address.change', onAddressChange.call(this))
        .on('booktime.change', onBooktimeChange.call(this))
        .on('material.change', onMaterialChange.call(this))
        .on('goods_subsidy.change', onSubsidyChange.call(this)) // 加价购
        .on('cart.user.change', onCartUserChange.call(this))
        .on('phoneNumber.change', (phoneNumber: string) =>
          // @ts-ignore
          this.setData({ phoneNumber, cellphone: phoneNumber })
        )
        .on('address.click', onClickCurrentAddress.call(this))
        .on('floor.change', onFloorChange.call(this))
      // .run(() => {
      //
      // });
      // @ts-ignore
      const {
        // @ts-ignore
        user_id,
        uc_user_id,
        openId = user_id
      } = (sqbBridge.getLoginInfo && sqbBridge.getLoginInfo()) || {}
      const storeMerchantInfo = {
        merchantId,
        storeId,
        storeSn,
        // paymentScene: getPaymentScene(this.manager.serviceTypeName),
        bizScene: getPaymentScene(
          this.manager.serviceTypeName,
          takeoutProfitSharing,
          supportCardPay
        ),
        userId: uc_user_id,
        uc_user_id
      }
      if (openId) {
        _.assign(storeMerchantInfo, { openId })
      }
      this.setData({ storeMerchantInfo })

      // 初始化订单主数据
      const params = {
        discount_params: {
          recharge_and_pay: JSON.parse(isStoredPaySelected)
        }
      }

      // 是否为麦当劳店铺
      const mccResult = sqbBridge.getMcc(MCDONALDS_STORE)
      if (mccResult instanceof Promise) {
        mccResult
          .then((isMcDonaldStore: any) => {
            if (_.isEmpty(isMcDonaldStore) && !_.isNull(isMcDonaldStore)) {
              this.setData({ isMcDonaldStore: true })
            }
          })
          .catch(() => {})
      }
      // @ts-ignore
      this.initPayload(params)
        // .then(async () => {
        //   // @ts-ignore
        //   return storage('emenu:useCashier');
        // })
        // .finally(() => this.setData({ loaded: true }))
        //@ts-ignore
        .then(() => {
          // 广告埋点曝光
          this.trackAds()
          this.setData({ loaded: true })
        })
        .catch(() => {
          // 当order/main接口报错的时候，不展示收银台且让收银台不抛错
          this.setData({ loaded: true, useCashier: false })
        })

      // 初始化订单额外数据
      this.initExtra()
      // this.initAdPre() // 初始化广告，获取traceid - 已注释，暂时不需要使用

      // 当用户切换时
      const subscribe = _.get(sqbBridge, 'observers.authLogin.subscribe')
      if (_.isFunction(subscribe))
        // @ts-ignore
        subscribe(
          // @ts-ignore
          onSwitchLogin({
            bridge: sqbBridge,
            context: this,
            // onSuccess: this.onSwitchLoginSuccess,
            // onFail: this.onSwitchLoginFail,
            cartSvc: this.manager.cartSvc
          })
        )

      // 隐私协议授权检查 - 外卖订单需要检查
      // if (this.serviceType === 1) {
      //   this.requestPolicy(this.serviceType)
      // }

      // 结算页浮窗对接AOP
      this.loadFloatAOP()
    },
    ready() {
      this.call('on', 'show', this.onShow.bind(this))
      const terminal = this.getTerminal()
      // 更新营销组件主题
      this.updateMkssTheme()
      // initAuthUserData(this)// 初始化授权数据
      this.setData({ mounted: true, terminal })
      checkSession(this)
      this.$track('SmMpPageDisplay', {})
    },
    detached() {
      emenuChannel.setMkCustomInfo(this.initialMkCustomInfo, true)
      this.call('off', 'show', () => ({}))
      this.manager
        .unTap('order.before')
        .unTap('order.order')
        .unTap('order.success')
        .unTap('order.fail')
        .unTap('order.pay_success')
        .unTap('order.pay_fail')
        .unTap('order.pay_cancel')
        .unTap('order.pay_before')
        .off('address.change')
        .off('booktime.change')
        .off('take_out_order.type.change')
        .off('paytype.change')
        .off('subscribe_order.type.change')
        .off('cart.user.change')
        .off('material.change')
        .off('goods_subsidy.change')
        .off('phoneNumber.change')
        .off('address.click')
        .off('floor.change')

      const payChannel = this.data.payChannel
      payChannel.off('onPayFinish')
      payChannel.off('submit')
      payChannel.off('onPayToolChange')
      payChannel.off('onPayPluginPrepayChange')
      payChannel.off('receiveAmount')
      // payChannel.off('prepayAmountIsNotEnough')
      // @ts-ignore
      if (_.isFunction(payChannel.unregisterBeforeSubmitEvent) && this.hasRegisteredBeforeSubmit) {
        // @ts-ignore
        payChannel.unregisterBeforeSubmitEvent(this.boundBeforeSubmit)
        this.hasRegisteredBeforeSubmit = true
      }
    }
  },
  // @ts-ignore
  methods: {
    // 推荐加料方法
    onSelectMaterial(e) {
      const { materialIndex, recordId, material } = _.get(e, 'currentTarget.dataset')
      // const material = _.find(_.get(this.data.record, 'recommend_materials'), ({ id }) => id === materialId);
      let count = 0
      if (material.selected > 0) {
        // 减购
        count--
      } else {
        // 加购
        count++
      }
      this.onMaterialChange({ detail: { recordId, material, materialIndex, count } })
      // this.$emit('change', {
      //   recordId,
      //   material,
      //   materialIndex,
      //   count
      // })
    },
    // for e2e method
    callManager(eventName, ...data) {
      this.manager.call(eventName, ...data)
    },

    // 是否显示积分弹窗
    onPointsVisibleChange(e) {
      this.setData({ isShowPointsPopup: _.get(e, 'detail.visible', false) })
    },
    // 积分弹窗点击确定
    onPointsComfirm() {
      this.setData({ isShowPointsPopup: false })
      this.fetchDiscount(true)
    },
    // 打开积分弹窗
    onPointsDiscountTap() {
      this.setData({ isShowPointsPopup: true })
    },

    async onBeforeSubmitEvent() {
      console.log('#onBeforeSubmitEvent')
      const { serviceTypeName } = this.data
      if (serviceTypeName === 'take_out_order') {
        wx.showLoading({
          title: '',
          mask: true
        })
        await fetchDeliverFee.call(this, { type: 'beforeSubmit' })
      }
    },
    goBack() {
      this.call('navigateBack', { delta: 1 })
      this.setData({ presetTimeDict: {} })
    },
    /**
     * 结算页浮窗对接AOP
     */
    loadFloatAOP() {
      // @ts-ignore
      const { sqbBridge } = this.data

      this.manager.adsenseSvc
        .fetchAopContent({
          field_code: isWeixin() ? AOP_WX_SUBMIT_FLOAT_CODE : AOP_MY_SUBMIT_FLOAT_CODE,
          field_style: 'wechat_orderpage_popup'
        })
        .then((data: { records: any }) => {
          const { records } = data
          if (records && records.length) {
            const bannerData = records[0]
            const floatingBubbleData = {
              pic: floatingBubblePic,
              ...bannerData,
              ...(bannerData.extra || {})
            }
            this.setData({ floatingBubbleData })
            sqbBridge.track('SmMpPageDisplay', {
              sm_uiview_name: '结算页浮窗'
            })
          }
        })
    },
    // 浮窗按钮关闭
    onFloatingBubbleClose(props) {
      storage('floatingBubbleVisible', _.get(props, 'detail'))
    },

    // 处理加料
    async onMaterialChange(e) {
      const params = _.get(e, 'detail')
      this.manager.emit('material.change', params)
    },

    getTrackCommonParams() {
      const params: any = {
        sm_page_name: '提交订单页'
      }
      if (this._SQBADVERTISING) {
        params.sm_ass_id = this._SQBADVERTISING.getTrackId()
      }

      return params
    },

    onSubsidyAddClick: _.throttle(
      async function (this: any, e) {
        const { sqbBridge } = this.data
        const goods = e.detail.item
        const { name, activity_spec } = goods
        this.manager.emit('goods_subsidy.change', { type: 'add', goods })
        const params = this.getTrackCommonParams()
        params.sm_uiview_name = `${name}${activity_spec || ''}-加购`

        sqbBridge.track('SmMpUIViewClick', params)
      },
      500,
      { trailing: false }
    ),

    onSubsidyDecClick: _.throttle(
      async function (this: any, e) {
        const { sqbBridge } = this.data
        const goods = e.detail.item
        const { name, activity_spec } = goods
        this.manager.emit('goods_subsidy.change', { type: 'minus', goods })

        const params = this.getTrackCommonParams()
        params.sm_uiview_name = `${name}${activity_spec || ''}-减购`

        sqbBridge.track('SmMpUIViewClick', params)
      },
      500,
      { trailing: false }
    ),

    trackAds() {
      // @ts-ignore
      const { brandActivity, sqbBridge, serviceType, isWeixin } = this.data

      if (!brandActivity) return
      const { items = [] } = brandActivity

      const params = this.getTrackCommonParams()
      params.sm_uiview_name = '品牌购活动底图'
      sqbBridge.track('SmMpUIViewDisplay', params)

      _.forEach(items, (item: any) => {
        const { name, activity_spec } = item
        const params = this.getTrackCommonParams()
        params.sm_uiview_name = `${name}${activity_spec || ''}`
        sqbBridge.track('SmMpUIViewDisplay', params)
      })
    },

    /**
     * 支付组件回调
     * @param {*} e
     */
    onPaytypeChange(e) {
      ECHO('onPaytypeChange', e)
      this.manager.emit('paytype.change', _.get(e, 'detail', {}))
    },

    onConfirmInvalidGoodsDialog() {
      this.go(-1)
    },

    onClickSelectAddress(e) {
      this.manager.emit('address.click', e.detail)
    },

    onCloseSelectAddress() {
      this.setData({ selectAddressSheetShow: false })
    },

    async onSelectAddressSubmit(e: { detail: { value: unknown } }) {
      const id = e.detail.value
      if (id === this.data.currentAddressId) return

      this.manager.emit('address.change', id)
    },

    onClickRemark() {
      // @ts-ignore
      const { remark } = this.data
      storage('pageRemark', remark || '')
      this.go('page-remark')
    },

    // 支付逻辑
    onPay: _.throttle(
      function (this: any) {
        // this._debouncePay()
        const {
          serviceTypeName,
          // isStoredPaySelected,
          isNeedStoredRecharge,
          sign,
          hasAgreement,
          paytype,
          // @ts-ignore
          isStoredPaySelected,
          showMkssStoredActivityInSubmit,
          showStoredPay,
          showSodexoPay,
          isNativePay,
          payway,
          sqbBridge,
          useCashier
        } = this.data

        this.setPaying(true)

        // @track 储值并支付点击事件
        if (paytype === CONSTANT.STORED_PAY_TYPE) {
          this.call('track', 'MKOrderPayClick', {
            click_name: isNeedStoredRecharge ? '储值并支付' : '储值支付'
          })
        }

        this.valid()
          .then(async () => {
            // sls 记录是否可以显示储值卡
            await slsShowStoredPay.call(this, sqbBridge, {
              isStoredPaySelected,
              showMkssStoredActivityInSubmit,
              showStoredPay,
              showSodexoPay,
              isNativePay,
              payway
            })

            const isPreOrder = CONSTANT.PRE_ORDER === serviceTypeName
            const isTakeOutOrder = CONSTANT.TAKE_OUT_ORDER === serviceTypeName

            // 外卖需要验证协议
            const shouldConfirmAgreement = (isTakeOutOrder || isPreOrder) && !hasAgreement && sign
            if (shouldConfirmAgreement) this.manager.confirmTakeoutAgreement()

            const terminal = this.getTerminalFromStorage()
            let orderTerminal = {}
            if (terminal && serviceTypeName == CONSTANT.SUBSCRIBE_ORDER) {
              orderTerminal = this.createTerminalForOrder(terminal)
            }

            // 是否是校内订单逻辑处理
            if (isAlipay()) {
              if (_.isFunction(my.getAuthCode)) {
                my.getAuthCode({
                  // 订单服务授权：order_service。如需同时获取用户多项授权，可在 scopes 中传入多个 scope 值。
                  // @ts-ignore
                  scopes: ['order_service'],
                  success: () => {
                    this.manager.submit(
                      { service_type: this.serviceType, ...orderTerminal },
                      useCashier
                    )
                  },
                  // 订单服务授权失败，根据自己的业务场景来进行错误处理
                  fail: err => {
                    this.manager.submit(
                      { service_type: this.serviceType, ...orderTerminal },
                      useCashier
                    )
                    sqbBridge.sls('INFO', {
                      type: 'myGetAuthCodeErr',
                      data: err
                    })
                  }
                })
              } else {
                this.manager.submit(
                  { service_type: this.serviceType, ...orderTerminal },
                  useCashier
                )
              }
            } else {
              this.manager.submit({ service_type: this.serviceType, ...orderTerminal }, useCashier)
            }

            sqbBridge.sls('INFO', {
              type: 'submit:order:terminal',
              data: { ...orderTerminal, serviceTypeName: this.data.serviceTypeName }
            })
          })
          .catch(reason => {
            this.setPaying(false)
            reason && wx.showToast({ title: reason, duration: 3000, icon: 'none' })
            return
          })
      },
      800,
      { trailing: false }
    ),
    valid(): Promise<string | boolean> {
      return new Promise((resolve, reject) => {
        const {
          serviceTypeName,
          phoneNumber,
          requirePhone,
          cellphone,
          packType,
          hasAgreement,
          sign,
          cancelAuthPhoneNumber,
          defaultAddress,
          addressDisabled,
          realDeliveryFee,
          sqbBridge,
          errors,
          presetTimeDict,
          isMcDonaldStore,
          mandatoryPhoneAuth
        } = this.data

        // 判断订单类型
        const isPreOrder = CONSTANT.PRE_ORDER === serviceTypeName
        const isSubscribeOrder = CONSTANT.SUBSCRIBE_ORDER === serviceTypeName
        const isTakeOutOrder = CONSTANT.TAKE_OUT_ORDER === serviceTypeName

        // 获取手机号验证正则
        const phoneRegex = new RegExp(sqbBridge.getConfig('PHONE_REGEX'))

        // 检查各种无效情况
        const isInvalidAgreement = (isTakeOutOrder || isPreOrder) && !hasAgreement && !sign
        const isInvalidPreOrderPhone = isPreOrder && !cancelAuthPhoneNumber
        const isInvalidSubscribePhone = isSubscribeOrder && requirePhone && !cancelAuthPhoneNumber
        const isInvalidPaytype = isSubscribeOrder && (packType === 'none' || !packType)
        const isInvalidAddress =
          isTakeOutOrder &&
          ((defaultAddress && !defaultAddress.id) ||
            addressDisabled ||
            realDeliveryFee == undefined)
        const isMandatoryPhoneAuth = isSubscribeOrder && !cellphone && mandatoryPhoneAuth

        // 定义验证规则数组
        const validationRules = [
          // 检查服务协议
          {
            condition: () => isInvalidAgreement,
            message: this.t('请先阅读并同意收钱吧服务协议'),
            action: msg => reject(msg)
          },
          // 检查地址和送达时间 (外卖订单)
          {
            condition: () => isInvalidAddress && !presetTimeDict[serviceTypeName],
            message: this.t('请确认收货地址和送达时间'),
            action: msg => {
              this.setData({ isShowAddressAndTimeTip: true, scrollTop: 0 })
              setTimeout(() => {
                this.setData({ isShowAddressAndTimeTip: false })
              }, 2000)
              reject(msg)
            }
          },
          // 仅检查地址 (外卖订单)
          {
            condition: () => isInvalidAddress && presetTimeDict[serviceTypeName],
            message: this.t('请确认收货地址'),
            action: msg => {
              this.setData({ isShowAddressTip: true, scrollTop: 0 })
              setTimeout(() => {
                this.setData({ isShowAddressTip: false })
              }, 2000)
              reject(msg)
            }
          },
          // 检查送达时间或自取时间
          {
            condition: () =>
              (isTakeOutOrder && !isInvalidAddress && !presetTimeDict[serviceTypeName]) ||
              (isPreOrder &&
                !isInvalidAddress &&
                !presetTimeDict[serviceTypeName] &&
                !isMcDonaldStore),
            message: isTakeOutOrder ? this.t('请确认送达时间') : this.t('请选择自取时间'),
            action: msg => {
              this.setData({ isShowTimeTip: true, scrollTop: 0 })
              setTimeout(() => {
                this.setData({ isShowTimeTip: false })
              }, 2000)
              reject(msg)
            }
          },
          // 检查是否需要强制授权手机号
          {
            condition: () => isMandatoryPhoneAuth,
            message: '',
            action: msg => {
              this.setData({ showAuthPhoneDialog: true })
              reject(msg)
            }
          },
          // 检查预订单电话号码是否存在
          {
            condition: () => isInvalidPreOrderPhone && !phoneNumber,
            message: '',
            action: msg => {
              this.setData({ showPhoneNumberDialog: true })
              reject(msg)
            }
          },
          // 检查预订单电话号码格式
          {
            condition: () =>
              isInvalidPreOrderPhone &&
              (String(phoneNumber).length !== 11 || !phoneRegex.test(phoneNumber)),
            message: this.t('手机格式不正确'),
            action: msg => reject(msg)
          },
          // 检查订阅订单电话号码是否存在
          {
            condition: () => isInvalidSubscribePhone && !cellphone,
            message: '',
            action: msg => {
              this.setData({ showPhoneNumberDialog: true })
              reject(msg)
            }
          },
          // 检查订阅订单电话号码格式
          {
            condition: () =>
              isInvalidSubscribePhone &&
              cellphone &&
              (String(cellphone).length !== 11 || !phoneRegex.test(cellphone)),
            message: this.t('手机格式不正确'),
            action: msg => reject(msg)
          },
          // 检查支付类型
          {
            condition: () => isInvalidPaytype,
            message: this.t('请先选择就餐方式'),
            action: msg => {
              this.setData({ isShowSelectTip: false }, () =>
                this.setData({ isShowSelectTip: true })
              )
              reject(msg)
            }
          },
          // 检查配送费错误
          {
            condition: () =>
              errors &&
              errors.length &&
              _.find(errors, { code: DELIVERY_FEE_ERROR_CODE }) &&
              isTakeOutOrder,
            message: this.t('获取配送费失败，请确认配送地址'),
            action: msg => reject(msg)
          }
        ]

        // 执行验证规则，如果所有规则都通过则resolve
        const failedRule = _.find(validationRules, rule => rule.condition())
        if (failedRule) {
          failedRule.action(failedRule.message)
        } else {
          resolve(true)
        }

        sqbBridge.sls('INFO', {
          type: 'submit:valid',
          data: {
            isInvalidAgreement,
            requirePhone,
            isInvalidAddress,
            isInvalidPaytype,
            isInvalidSubscribePhone,
            isInvalidPreOrderPhone,
            phoneNumber,
            cellphone,
            cancelAuthPhoneNumber,
            serviceTypeName,
            realDeliveryFee,
            packType
          }
        })
      })
    },

    onEdit(e) {
      const { id } = e.detail
      let query = {}
      if (id) {
        query = { id }
      }
      this.go('page-address', query)
      // this.data.sqbBridge.navigateTo({
      //   path: '/E1QDEFPQUJWB/u0w1f83ptdyv/index',
      //   query
      // })
      this.setData({ selectAddressSheetShow: false })
    },

    // 选择店内或者打包
    onMealTypeChange(e) {
      this.manager.emit('subscribe_order.type.change', e.detail)
    },

    // 选择外卖就餐方式
    onSelectedType(e) {
      this.manager.emit('take_out_order.type.change', e.detail)
    },

    onInputPhone(e) {
      this.manager.emit('phoneNumber.change', e.detail)
    },

    onPhoneInputBlur(e) {
      this.manager.emit('phoneNumber.change', _.get(e, 'detail.value'))
    },

    onCloseStored(e) {
      const params = e.detail
      const { paySuccess } = params || {}
      this.setData({ isShowStoredCard: false })
      // ?
      paySuccess && this.manager.fetchStoredCardBalance()
    },

    onStored() {
      this.setData({ isShowStoredCard: true })
    },

    async onStoredActivityDialogSuccess() {
      this.setData({ isShowStoredCard: false })
      this.$clearToast()
    },

    onCancelAlipay(e) {
      this.setData({ isShowAlipayModal: false, paying: !!e.detail, huabeiParams: [] })
    },

    onInputAuthPhone(e) {
      const { sqbBridge } = this.data
      const cellphone = _.get(e, 'detail.value', '')
      const _cellphone = this.phoneVerify && this.phoneVerify.verifyPhone(cellphone)
      // 当输入的手机号长度大于11位时禁止其输入
      const _phone =
        this.phoneVerify && this.phoneVerify.stringNumberLen(_cellphone) > 11
          ? _cellphone.substring(0, 11)
          : _cellphone

      this.setData({ cellphone: _phone })
      // SMART-13359
      sqbBridge.sls('INFO', {
        type: 'onInputAuthPhone',
        data: { data: { detail: e.detail } }
      })
    },

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onTapAuthUser() {},

    async onAuthPhoneCallback(e) {
      const { sqbBridge } = this.data
      const { phoneNumber: cellphone } = e.detail || {}
      if (cellphone) {
        // 上报提交页面手机号授权隐私场景 - 使用动态配置
        const serviceTypeName = this.data?.serviceTypeName || CONFIG.SUBSCRIBE_ORDER
        this.reportPrivacySceneOnce(`page-submit-phone-${serviceTypeName}`)

        this.setData({ cellphone })
        sqbBridge.sls('INFO', { type: 'authPhone:success', data: { cellphone } })
      }
    },

    onTapAgreement() {
      const { sqbBridge } = this.data
      sqbBridge.navigateTo({
        path: '/pages/web/index',
        query: {
          url: 'https://smart-static.wosaimg.com/99zhe/agree/takeoutAgreement.html'
        }
      })
    },

    onConfirmAgreement() {
      const { sign } = this.data
      this.setData({ sign: !sign })
    },

    requestPolicy(serviceType) {
      // 检查配置开关，如果配置为 false 则跳过授权检查
      const privacyAuthEnabled = _.get(this.data, 'sqbBridge.getConfig')?.('PRIVACY_AUTH_ENABLED')
      if (privacyAuthEnabled === false) {
        return // 配置禁用，跳过授权检查
      }

      // 检查是否已有授权记录
      if (isPrivacyAuthorized()) {
        return // 已成功授权，跳过请求
      }

      // @ts-ignore
      requestPolicyWithBridge
        .call(this.data.sqbBridge, [
          // {
          //   agreementType: 'sqb_c_privacy',
          //   signObjId: '',
          //   // independent: true,
          //   //   force: true
          // },
          {
            agreementType: 'sqb_c_auth',
            once: true
            // signObjId: '',
            // force: true
            // independent: true,
          }
        ])
        .then(e => {
          const status = _.get(e, 'status')
          if (status) {
            setPrivacyAuthStatus(status)
          }
          if (status === 'REFUSE' && serviceType === 1) {
            wx.showModal({
              title: '',
              content: '只有您同意相关协议和政策并提供必要信息的前提下，我们才能继续为您提供服务',
              confirmText: '我知道了',
              showCancel: false,
              success: res => {
                // @ts-ignore
                if ((isWeixin() && res.confirm) || (isAlipay() && res.success)) {
                  this.requestPolicy(serviceType)
                }
              }
            })
          }
        })
    },

    onCancelPhoneNumber() {
      this.setData({ showPhoneNumberDialog: false, cancelAuthPhoneNumber: true })
      this.onPay()
    },

    onEnsurePhoneNumber() {
      this.setData({ showPhoneNumberDialog: false })
    },

    /**
     * 支付时授权手机号失败
     */
    onPayAuthPhoneError() {
      this.$toast('授权失败，请稍后再试')
    },

    /**
     * 支付时授权手机号拒绝
     */
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onPayAuthPhoneDeny() {},

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onAuthPhoneReject() {},

    async onAuthPhoneClick() {
      const phone = await aliAuthGetPhone(this, true)
      this.setData({ cellphone: _.get(phone, 'phoneNumber') })
    },

    // 强制授权手机号取消逻辑
    onCancelAuthPhone() {
      this.setData({ showAuthPhoneDialog: false })
    },

    // 强制授权手机号确认逻辑
    onConfirmAuthPhone(e) {
      this.onAuthPhoneCallback(e)
      this.setData({ showAuthPhoneDialog: false })
    },

    async initPayload(params: TFetchPayloadParams = {}) {
      this.$loading('加载中...')
      const { sqbBridge, useCashier } = this.data
      if (useCashier) {
        this.setPaying(true)
      }
      // 营销储值组件
      await this.manager.checkMkssStatus(
        params.order_type || this.manager.serviceTypeName,
        async val => {
          this.setData(val)
          // sls 记录是否可以显示储值卡
          await slsShowStoredPay.call(this, sqbBridge, val)
        }
      )
      // 初始化索迪斯支付SDK
      SodexoPaySDK.init({ sqbBridge, request: sqbBridge.request })
      const {
        isNeedStoredRecharge,
        paytype,
        payway,
        usingPayTools,
        presetTimeDict,
        serviceTypeName
      } = this.data
      _.set(params, 'discount_params.recharge_interest_id', this.interestId)
      _.set(params, 'discount_params.mk_custom_info', emenuChannel.getMkCustomInfo())
      if (!_.get(params, 'discount_params.recharge_and_pay')) {
        _.set(
          params,
          'discount_params.recharge_and_pay',
          paytype === CONSTANT.STORED_PAY_TYPE && isNeedStoredRecharge
        )
        if (payway) {
          _.set(params, 'discount_params.payway', payway)
        }
      }
      // 组合支付需要传该参数
      if (usingPayTools) {
        _.set(params, 'discount_params.usingPayTools', usingPayTools)
      }

      const preset_time = presetTimeDict[serviceTypeName] || -1

      _.set(params, 'preset_time', preset_time)

      return (
        this.manager
          .fetchPayload(params)
          // 优惠文本信息处理
          .then(payload => {
            this.$clearToast()
            const { discount, originCart } = payload
            updateMkCustomInfoByDiscount(discount)
            this.manager.doDiscount(
              { discount, originCart },
              data => (payload = { ...payload, ...data })
            )
            return payload
          })
          .then(async payload => {
            // 当实付金额为0时，不使用统一收银台
            //@ts-ignore
            const { paidAmount, discounts } = payload
            // 增加Apollo开关
            const IS_ENABLE_ZERO_PAY = sqbBridge.getConfig('IS_ENABLE_ZERO_PAY')
            let param = {
              useCashier: true,
              _is_zero_pay: false,
              IS_ENABLE_ZERO_PAY,
              mandatoryPhoneAuth: false
            }
            if (paidAmount == 0) {
              _.assign(extraPayParams, {
                redeem_digest: _.get(discounts, 'redeem_digest'),
                total_discount: _.get(discounts, 'total_discount')
              })
              param = { useCashier: false, _is_zero_pay: true, IS_ENABLE_ZERO_PAY }
              // 0元支付的时候从 main 接口获取优惠信息透传给积分组件
              // 获取积分抵扣项的数据,传给积分组件
              this.getMemberPointsParams(discounts)
            }
            // 拼团逻辑处理
            const campusActivityParams = {
              serviceType: _.get(payload, 'serviceType'),
              groupId: '',
              groupBuyingActivity: {}
            }
            if (!isAlipay()) {
              let { campusActivity, groupBuyingActivity } = this.getQuery()
              if (groupBuyingActivity) {
                try {
                  groupBuyingActivity = JSON.parse(decodeURIComponent(groupBuyingActivity))
                  campusActivityParams.groupBuyingActivity = groupBuyingActivity
                } catch (err) {}
              }

              if (campusActivity) {
                try {
                  campusActivity = JSON.parse(
                    decodeURIComponent(decodeURIComponent(campusActivity))
                  )
                  campusActivityParams.groupId = _.get(campusActivity, 'groupId')
                } catch (err) {}
              }
            }
            // 是否强制授权手机号
            const mandatoryPhoneAuthWhiteList =
              sqbBridge.getConfig('MANDATORY_PHONE_AUTH_WHITE_LIST') || []
            const storeSn = sqbBridge.getStore('storeSn')
            if (
              _.isArray(mandatoryPhoneAuthWhiteList) &&
              mandatoryPhoneAuthWhiteList.includes(storeSn)
            ) {
              param.mandatoryPhoneAuth = true
            }
            this.setData({ ...param, ...payload, campusActivityParams })
            if (payload.serviceType === CONSTANT.SERVICE_TAKE_OUT_TYPE) {
              // 外卖更新时间
              this.setBookTimes()
              this.updateCurrentTime()
            }
            this.getTotalPrice() // 总价, 主要营销组件使用originalTotal
            return payload
          })
          .then(async payload => {
            return payload
          })
          .then(payload => {
            if (useCashier && !this.data.isFetchingDiscount) {
              this.setPaying(false)
            }
            const { errors } = payload || {}
            // 应该阻断下单
            const shouldBlockOrder =
              // @ts-ignore
              errors &&
              errors.length &&
              _.some(errors, error => {
                // @ts-ignore
                return BLOCK_ERROR_CODES.includes(error.code)
              })
            if (shouldBlockOrder) {
              const errMsg = _.get(errors, '0.message')
              throw new Error(errMsg)
            }
          })
          .catch(error => {
            if (useCashier && !this.data.isFetchingDiscount) {
              this.setPaying(false)
            }
            console.error(error)
            this.$clearToast()
            this.alert(error.message, '错误信息', () => {
              this.closeAlert()
              this.go(-1)
            })
            throw new Error()
          })
      )
    },
    updateCurrentTime() {
      const { presetTimeDict, timerIdexMap, bookTimes, isRetail } = this.data
      this.manager.updateCurrentTime({ presetTimeDict, timerIdexMap, bookTimes, isRetail }, data =>
        this.setData(data)
      )
    },
    initExtra(params: TFetchExtraParams = {}) {
      return (
        this.manager
          .fetchExtra(params)
          .then(async extra => this.setData(extra))
          // 爱心餐
          .then(() => this.manager.getLoveDonateConfig().then(config => this.setData(config)))
          .catch(error => {
            this.$clearToast()
            console.error(error)
          })
      )
    },
    initAdPre() {
      const { sqbBridge, isWeixin } = this.data
      if (!isWeixin) return
      //@ts-ignore
      const env = sqbBridge.getMiniProgramProfile()
      const WOSAI_ENV = env !== 'development' ? 'prod' : 'beta'
      this._SQBADVERTISING = new SQBADVERTISING({
        WOSAI_ENV,
        adLoad: res => console.log(res),
        adError: err => console.error(err),
        onClose: cl => console.log(cl)
      })
    },
    editPeople() {
      this.setData({ showSelectUserCount: true })
    },
    selectUserCountCallback(e) {
      const { count } = e.detail
      this.manager.emit('cart.user.change', count)
    },
    cancelSelectUserCallback() {
      this.setData({ showSelectUserCount: false })
    },
    resetRecommendMaterial() {
      this.setData({ recommendMaterialRecordsMapping: {} })
    },
    async onShow() {
      // @ts-ignore
      const { sqbBridge } = this.data
      const loginInfo = sqbBridge.getLoginInfo()
      // @ts-ignore
      onSwitchLogin({
        bridge: sqbBridge,
        context: this,
        cartSvc: this.manager.cartSvc
      })({ detail: { loginInfo } })

      // @ts-ignore
      const pageRemark = storage('pageRemark') || ''
      // @ts-ignore
      this.setData({ remark: pageRemark, floatingBubbleVisible: storage('floatingBubbleVisible') })
      const { mounted } = this.data
      if (!mounted) return
    },
    clickSelectTime(e) {
      const { isRetail, PRE_ORDER, RETAIL_ORDER } = this.data
      let target = e.detail
      if (isRetail && target === PRE_ORDER) {
        target = RETAIL_ORDER
      }
      this.setData({ selectTimeSheetShow: true, preType: target }, () =>
        this.setData({ damBoard: true })
      )
    },
    closeSelectTime(e) {
      const detail = e.detail
      this.manager.emit('booktime.change', detail)
    },
    async setPaying(flag: boolean) {
      if (!flag) {
        this.data.payChannel.stopSubmit()()
        await delay(300)
      }

      this.setSubmitButtonStatus(flag)
    },
    /**
     * 设置支付按钮状态
     * @param flag 是否禁用支付按钮
     */
    setSubmitButtonStatus(flag: boolean) {
      this.setData({ paying: flag })
      this.data.payChannel.setSubmitButtonStatue()({
        disabled: flag
      })
    },
    getTotalPrice() {
      const { serviceTypeName, deliverFee } = this.data
      let totalPrice = this.getTradeDiscountTotal()
      if (serviceTypeName === CONSTANT.TAKE_OUT_ORDER) {
        const { realDeliveryFee = 0 } = deliverFee || {}
        totalPrice += realDeliveryFee
      }
      return totalPrice
    },
    getDiscountedPrice(discount) {
      const { serviceTypeName, deliverFee } = this.data
      const { total_discount = 0 } = discount || {}
      const { reductionAmount = 0 } = deliverFee || {}
      const discountNum =
        total_discount + (serviceTypeName === CONSTANT.TAKE_OUT_ORDER ? reductionAmount : 0)
      this.setData({ discountNum })
    },
    getTradeDiscountTotal() {
      const { originCart, packAmount: packFee, packed, serviceTypeName } = this.data
      let totalPrice = _.get(originCart, 'total_price', 0)
      if (
        +packed === 1 ||
        _.includes([CONSTANT.TAKE_OUT_ORDER, CONSTANT.PRE_ORDER], serviceTypeName)
      ) {
        totalPrice += packFee
      }
      this.setData({ originalPrice: totalPrice })
      return totalPrice
    },
    // 取消alert
    closeAlert() {
      const { sqbBridge } = this.data
      console.log('closeAlert')
      this.setData({
        alert: {
          visible: false,
          title: '',
          content: '',
          ok: () => {},
          cb: () => {}
        }
      })

      const { originCart } = this.data
      // 购物车无数据时，返回首页
      if (originCart && !originCart.records.length) {
        // this.redirect('page-home')
        const storeId = sqbBridge.getStore('storeId')
        return this.redirect('page-home', { storeId, force: true })
      }
    },
    onCustomerToMember() {
      this.fetchDiscount(true)
    },
    /**
     * 获取优惠详情
     * @param {boolean} refresh 是否执行优惠处理逻辑
     * @param opts
     */
    async fetchDiscount(refresh = true, opts = {}) {
      const {
        serviceTypeName,
        paytype,
        payway,
        isNeedStoredRecharge,
        originCart,
        deliverFee,
        usingPayTools,
        sqbBridge
      } = this.data
      extraPayParams = {
        redeem_digest: null,
        total_discount: null
      }
      const params = {
        serviceTypeName,
        isNeedStoredRecharge,
        originCart,
        paytype,
        payway,
        recharge_interest_id: this.interestId,
        deliverFee
      }
      const DISCOUNT_FETCH_TIMEOUT = sqbBridge.getConfig('DISCOUNT_FETCH_TIMEOUT') || 10000
      const setDiscountStatus = (flg: boolean) => {
        this.setPaying(flg)
        this.setData({ isFetchingDiscount: flg })
      }

      if (usingPayTools) {
        _.assign(params, { usingPayTools })
      }

      setDiscountStatus(true)
      // DISCOUNT_FETCH_TIMEOUT 后重置支付按钮状态
      // 避免接口请求超时，导致支付按钮一直置灰
      if (this.discountTimer) clearTimeout(this.discountTimer)
      this.discountTimer = setTimeout(() => {
        setDiscountStatus(false)
      }, DISCOUNT_FETCH_TIMEOUT)

      return (
        this.manager
          .fetchDiscount(params, opts, data => {
            // this.$clearToast();
            const isStoredRedeemDiscount = _.get(data, 'discount.redeem_details', []).some(
              item => !!item.gift_card_discount_detail
            )

            this.update({ ...data, isStoredRedeemDiscount })

            _.assign(extraPayParams, {
              redeem_digest: _.get(data, 'discount.redeem_digest'),
              total_discount: _.get(data, 'discount.total_discount')
            })

            if (refresh) {
              const { discount, originCart } = this.data
              this.manager.doDiscount({ discount, originCart }, data => this.update(data))
              this.getDiscountedPrice(discount) // 获取优惠后的价格，用于payment-summary优惠价格展示
            }
            // 获取积分抵扣项的数据,传给积分组件
            this.getMemberPointsParams(data.discount)
          })
          // .then(() => {
          //   this.$clearToast();
          // })
          .then(() => {
            // 重新监听统一收银台
            setupPayPluginListeners.call(this)
          })
          .catch(err => {
            console.error({ err })
            _.assign(extraPayParams, {
              redeem_digest: null,
              total_discount: null
            })
            if (_.get(err, 'source') === 'fetchCashierParams') {
              if (_.get(err, 'code') == 50008) {
                // 购物车商品清空的错误提示
                this.$alert({
                  title: '提示',
                  message: '购物车商品清空，请重新加购',
                  confirmButtonText: '去加购',
                  showCancelButton: false
                }).then(() => {
                  // 回到点单页
                  this.go(-1)
                })
              } else {
                // 其他错误提示
                this.$alert({
                  title: '提示',
                  message: _.get(err, 'message', '系统错误'),
                  confirmButtonText: '我知道了',
                  showCancelButton: false
                }).then(() => {
                  // 回到点单页
                  this.go(-1)
                })
              }
            }
            this.$clearToast()
          })
          .finally(() => {
            setDiscountStatus(false)
          })
      )
    },

    // 获取透传给积分组件的参数
    getMemberPointsParams(discount) {
      const _pointRedeem =
        _.find(_.get(discount, 'redeem_details', []), item => item.sub_type === 35) || {}
      this.setData({ pointsRedeemDetail: _pointRedeem })
    },

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    bindError() {},
    themeUI(config) {
      const { themeName } = config
      const isHeytea = themeName === 'heytea'
      const ismcDonald = themeName === 'mcDonald'
      const isKFC = themeName === 'kfc'
      let backColor = '#FFFFFF'
      let backgroundColor = '#FFFFFF'
      if (isHeytea || ismcDonald) {
        backColor = '#000000'
      }
      if (config.themeName === 'default' || config.themeName === 'kfc') {
        backgroundColor = '#000000'
      }

      if (config.themeName === 'mcDonald') {
        this.call('setNavigation', {
          backgroundColor: '#FFFFFF',
          homeButton: false,
          backColor: '#000000',
          custom: true
        })
      } else {
        this.call('setNavigation', {
          backgroundColor,
          custom: true,
          homeButton: false,
          backColor
        })
      }

      let wechatPayLuginThemeName = 'classic'
      if (isHeytea) {
        wechatPayLuginThemeName = 'xicha'
      } else if (ismcDonald) {
        wechatPayLuginThemeName = 'mdd'
      } else if (isKFC) {
        wechatPayLuginThemeName = 'kfc'
      }

      this.setData({ wechatPayLuginThemeName })
    },

    /**
     * 判断外卖/自取是否是立即下单
     */
    setBookTimes() {
      const { serviceTypeName, bookTimes: predetermineTime, timerIdexMap } = this.data
      const timeIndex = timerIdexMap[serviceTypeName]['timeIndex']
      if (!_.isEmpty(predetermineTime)) {
        const currentTimeType =
          _.head(predetermineTime) && _.head(_.get(_.head(predetermineTime), 'times'))
        // 如果不是立即单弹出弹窗
        if (currentTimeType !== -1 && timeIndex === -1) {
          this.setData({
            selectTimeSheetShow: true,
            preType: serviceTypeName
          })
        } else if (currentTimeType === -1 && timeIndex === -1) {
          this.setData({
            selectTimeSheetShow: false,
            timerIdexMap: _.merge(timerIdexMap, {
              [serviceTypeName]: {
                dayIndex: 0,
                timeIndex: 0
              }
            })
          })
        }
      }
    },

    setMkCustomInfoAndFetchDiscount(payload) {
      if (_.isEmpty(payload)) return

      emenuChannel.setMkCustomInfo(payload)
      this.fetchDiscount(true)
    },

    /**
     * 点击店铺优惠
     */
    onStoreDiscountTap() {
      const { sqbBridge, config, cart } = this.data

      const mkCustomInfo = emenuChannel.getMkCustomInfo()
      const storeInfo = this.getStore() || {}
      const { storeId, merchantId } = storeInfo
      sqbBridge.navigateTo({
        path: UTILS.getPageUrl('page-coupon-preferential', sqbBridge.isJJZ),
        target: 'page',
        query: {
          themeName: config.themeName,
          currentBizId: _.get(mkCustomInfo, 'previous_biz_id'),
          storeId,
          merchantId
        },
        events: {
          setMkCustomInfo: this.setMkCustomInfoAndFetchDiscount.bind(this)
        },
        success: function (res) {
          // 通过eventChannel向被打开页面传送数据
          res.eventChannel.emit('onAcceptData', { cart, mkCustomInfo })
        }
      })
    },
    /**
     * 点击商品抵用券
     */
    onGoodsCouponTap() {
      const { sqbBridge, config, cart } = this.data
      const mkCustomInfo = emenuChannel.getMkCustomInfo()
      const store = this.getStore() || {}
      const { storeId, merchantId } = store

      sqbBridge.navigateTo({
        path: UTILS.getPageUrl('page-coupon-goods', sqbBridge.isJJZ),
        target: 'page',
        query: {
          themeName: config.themeName,
          storeId,
          merchantId
        },
        events: {
          setMkCustomInfo: this.setMkCustomInfoAndFetchDiscount.bind(this),
          // 购物车无对应商品抵用券时
          // 模态框“前往添加”按钮点击事件
          onToEmenuHome: () => {
            // FIXME: 用于解决iPhone12下，点击“前往添加”按钮后，页面无法跳转的问题
            setTimeout(this.goBack, 400)
          }
        },
        success: function (res) {
          // 通过eventChannel向被打开页面传送数据
          res.eventChannel.emit('onAcceptData', { cart, mkCustomInfo })
        }
      })
    },

    /**
     * 用户可选配送上楼
     * @param {*} e upstairs配送上楼 | downstairs配送到楼下
     */
    selectedUserDelivery(e) {
      const { userDeliveryData } = this.data
      const _userDeliveryData = _.cloneDeep(userDeliveryData)
      const id = e.detail
      let showDeliveryFloor = false
      if (id === 'upstairs') {
        showDeliveryFloor = true
        _.assign(_userDeliveryData[0], { status: true })
        _.assign(_userDeliveryData[1], { status: false })
      } else {
        // 当选择到配送到楼下时，当前楼层信息为0
        _.assign(_userDeliveryData[0], { status: false })
        _.assign(_userDeliveryData[1], { status: true })
      }

      this.setData({ showDeliveryFloor, userDeliveryData: _userDeliveryData }, () => {
        // 当用户选择配送上楼没点击确认时不请求配送费接口
        if (id !== 'upstairs') {
          this.manager.emit('floor.change')
        }
      })
    },

    /**
     * 选择配送上楼楼层
     * @param {*} e
     */
    onClickDeliveryFloor(e) {
      const selectDeliveryFloorIndex = e.currentTarget.dataset.index
      const { userDeliveryData } = this.data
      const _userDeliveryData = _.cloneDeep(userDeliveryData)
      _.assign(_userDeliveryData[0], { floorIndex: selectDeliveryFloorIndex })
      this.setData({ userDeliveryData: _userDeliveryData })
    },

    /**
     * 用户点击确定
     * @returns
     */
    onConfirmDeliveryFloor() {
      // @ts-ignore
      const { deliveryFloorData } = this.data
      let { userDeliveryData } = this.data

      const currentFloor =
        _.get(deliveryFloorData, `${userDeliveryData[0]['floorIndex']}.floor`) || null

      // 若当前没有选择楼层，则不执行后续操作
      if (!currentFloor) {
        this.$toast('请选择楼层')
        return
      }
      userDeliveryData = this.updateUserDeliveryData(currentFloor, deliveryFloorData)

      userDeliveryData[0].lastFloor = currentFloor

      this.setData({ userDeliveryData }, () => {
        if (userDeliveryData[0].floorIndex !== -1) {
          this.manager.emit('floor.change')
        }
      })

      this.onCloseDeliveryFloor()
    },

    /**
     * 用户点击取消或者弹层其他区域
     * @returns
     */
    onCancelDeliveryFloor() {
      const { deliveryFloorData } = this.data
      let { userDeliveryData } = this.data

      if (userDeliveryData[0].lastFloor) {
        userDeliveryData[0].currentFloor = userDeliveryData[0].lastFloor
      }
      userDeliveryData = this.updateUserDeliveryData(
        userDeliveryData[0].lastFloor,
        deliveryFloorData,
        true
      )
      this.setData({ userDeliveryData })
      this.onCloseDeliveryFloor()
    },

    /**
     * 关闭楼层信息
     */
    onCloseDeliveryFloor() {
      this.setData({ showDeliveryFloor: false })
    },

    /**
     * 管理配送上楼模块数据
     */
    updateUserDeliveryData(currentFloor, deliveryFloorData, isClear = false) {
      const { userDeliveryData } = this.data
      const _userDeliveryData = _.cloneDeep(userDeliveryData)
      // @ts-ignore
      const currentFloorIndex = _.findIndex(deliveryFloorData, item => item.floor === currentFloor)
      const currentFloorInfo = _.find(deliveryFloorData, item => item.floor === currentFloor)
      if (currentFloorIndex !== -1) {
        _.assign(_userDeliveryData[0], {
          floor: `帮我送到${currentFloorInfo.floorText}`,
          fee_desc: `需加收配送费${toCurrency(currentFloorInfo.fee, 0)}元`,
          currentFloor,
          status: true,
          floorIndex: currentFloorIndex,
          lastFloor: currentFloor
        })
        _.assign(_userDeliveryData[1], { status: false })
      } else if (isClear) {
        _.assign(_userDeliveryData[0], {
          lastFloor: '',
          floor: '',
          fee_desc: '足不出户享用美食',
          floorIndex: -1,
          status: false
        })
        _.assign(_userDeliveryData[1], { status: true })
      } else {
        _.assign(_userDeliveryData[0], { floorIndex: -1, status: false })
        _.assign(_userDeliveryData[1], { status: true })
      }
      return _userDeliveryData
    },
    createTerminalForOrder(terminal) {
      return {
        terminal_id: terminal.terminalId,
        areaId: terminal.areaId,
        table_id: terminal.tableId,
        name: terminal.name,
        tableName: terminal.tableName || terminal.name,
        terminal_sn: terminal.terminal_sn
      }
    }
  }
})

function handleOrderPayCancel(this) {
  return async result => {
    console.log('handleOrderPayCancel', result)
    const { client_sn: orderSn } = result
    await this.manager.paySvc.payCancel(orderSn)
    this.$toast('支付取消')
    this.redirect('page-order-detail', { order_sn: orderSn })
  }
}

function handleOrderPayFail(this) {
  return result => {
    const { client_sn: orderSn } = result.data || {}
    this.setPaying(false)
    fetchCart.call(this)
    this.$clearToast()

    if (orderSn) {
      return this.redirect('page-order-detail', { order_sn: orderSn })
    }

    this.$toast('支付失败')
    this.go(-1)
  }
}

/**
 * 订单支付成功处理函数
 *
 * @this Submit - Submit类的实例上下文
 *
 * 回调函数的执行流程如下：
 * 1. 从`order`对象中解构出`client_sn`属性，并将其赋值给`order_sn`。
 * 2. 调用`setPaying`方法，参数为`false`，表示支付过程已结束。
 * 3. 设置`mounted`数据属性为`false`。这样做是为了在页面重定向到订单详情页时再次触发`show`事件，从而触发`address.change`事件重新计算配送费。
 * 4. 调用`fetchCart`函数，更新购物车数据。
 * 5. 调用`sqb`对象的`hideLoading`方法，隐藏加载指示器。
 * 6. 重定向到订单详情页，`order_sn`作为查询参数。
 *
 * @example
 * // 这是一个使用此函数的示例：
 * const handleSuccess = handleOrderPaySuccess.call(submitInstance);
 * handleSuccess(payResult, order);
 */
function handleOrderPaySuccess(this) {
  // @ts-ignore
  return (payResultOrError, order) => {
    const { client_sn: order_sn } = order
    // 记录支付成功时间
    const { sqbBridge } = this.data
    sqbBridge.sls('INFO', {
      type: 'orderPaySuccess',
      duration: Date.now() - componentCreateAt,
      data: {
        order_sn
      }
    })

    this.setPaying(false)
    // 主要页面跳转至详情页， 触发show事件后，再触发address.change, 重新拉配送费
    this.setData({ mounted: false })
    // this.getData();
    fetchCart.call(this)
    this.$clearToast()
    this.redirect('page-order-detail', { order_sn })
  }
}

function checkBeforeAddOrder() {
  return new Promise((resolve, reject) => {
    // @ts-ignore
    wx.checkBeforeAddOrder({
      success(res) {
        resolve(res.data)
      },
      fail(err) {
        reject(err)
      }
    })
  })
}

function handleOrderBefore(this) {
  return async (order, cb) => {
    // 1242、1243
    // 支持门店快送
    // 预下单body增加mp_scene和wx_trace_id
    if (this.manager.isWxStoreDelivery()) {
      const result = await checkBeforeAddOrder()
      if (result) {
        const mp_scene = getMpScene()
        _.set(order, 'data.mp_scene', mp_scene)
        _.set(order, 'data.wx_trace_id', _.get(result, 'traceId'))
      }
    }
    cb(null, order)
  }
}

function handleOrderPayBefore(this) {
  return (orderRes, order, cb) => {
    if (_.get(orderRes, 'data.wap_pay_request')) {
      const sn = _.get(orderRes, 'data.sn')
      sn && _.set(orderRes, 'data.wap_pay_request.out_trade_no', sn)
    }
    cb(null, orderRes)
  }
}

function handleOrderFail(this) {
  return async orderResultOrError => {
    try {
      this.$clearToast()
      wx.hideLoading()
      const [modal_detail_value, alert_message_value] = UTILS.decodePayResult(orderResultOrError)
      if (modal_detail_value) {
        this.setData({ invalidGoodsList: modal_detail_value.check_fail_list })
      }

      if (alert_message_value) {
        const { error_code, error_msg } = alert_message_value
        let okText = ''
        if (error_code === '50015') {
          okText = '去选择'
        }
        this.alert(
          error_msg,
          async () => {
            // this.getData();
            await fetchCart.call(this)
            this.closeAlert()
            // 储值卡支付时出现：message: "交易异常，请稍候再试！" code:50004, 该订单购物车已为空，则直接回首页
            if (~~error_code === 50004) {
              this.go(-1)
            }
            // 50015:未选择必选品,此异常码执行回调的时候去点单首页
            if (error_code === '50015') {
              this.redirect('page-home', { fromPaySubmitOrder: 'batchNoMustOrderGoods' })
            }
          },
          okText
        )
      }

      this.setPaying(false)
      const cart = (await fetchCart.call(this, false)) || {}
      this.manager.emit(UTILS.CART_CHANGE_EVENT, cart)

      this.fetchDiscount(false)
    } catch (err) {
      console.log(err)
    }
  }
}

function handleOrderSuccess(this) {
  return ({
    data = <
      {
        sn: string
        acquiring: object
        client_sn: string
        wap_pay_request: { encrypt_data: string }
      }
    >{}
  }) => {
    const { sn, client_sn, acquiring, wap_pay_request = <{ encrypt_data: string }>{} } = data

    if (sn) {
      this.setData({ orderno: sn, client_sn })
    }

    // TODO: 设置统一收银台需要的支付参数

    // 索迪斯卡支付
    if (sn && client_sn) {
      const { paytype, sqbBridge, useCashier } = this.data
      if (paytype === CONSTANT.SODEXO_PAY_TYPE && SodexoPaySDK) {
        const hillsStage = sqbBridge.getHillsStage && sqbBridge.getHillsStage()
        const token = storage('token', undefined)
        const storeInfo = this.getStore()
        const { merchantId, merchantSn } = storeInfo

        const sodexoPaymentHeaders = sqbBridge.isJJZ
          ? {}
          : {
              'x-ca-stage': hillsStage,
              // Authorization: JWT,
              'Wosai-MiniProgramType': 'WECHAT',
              'Wosai-Scene': 'manual',
              'Wosai-Token': token
            }
        // 索迪斯卡支付
        SodexoPaySDK.requestPayment(
          {
            sn,
            // @ts-ignore
            clientSn: client_sn,
            merchantId,
            merchantSn,
            encryptData: wap_pay_request.encrypt_data
          },
          // @ts-ignore
          sodexoPaymentHeaders
        ).catch(err => {
          this.$toast(err.msg || '索迪斯支付失败，请重试')
        })
      }
      const wapPayRequest = _.get(acquiring, 'payment_voucher') || camelCaseKeys(wap_pay_request)

      if (wapPayRequest.tradeNo) {
        wapPayRequest.tradeNO = wapPayRequest.tradeNo
      }

      if (useCashier && wapPayRequest) {
        const { cashierBizParams, sqbBridge } = this.data
        const { uc_user_id } = sqbBridge.getLoginInfo()
        // const { storeId, merchantId } = this.store
        const { storeId, merchantId } = this.getStore()
        const rest = {
          storeId,
          merchantId,
          memberId: uc_user_id,
          useRequestOrderPayment: this.manager.isWxStoreDelivery()
        }
        ECHO('payChannel.requestPayment', {
          wapPayRequest,
          cashierBizParams,
          rest
        })
        // @ts-ignore
        sqbBridge.sls('INFO', {
          type: 'cashier-on-requestPayment',
          data: { data: { wapPayRequest, cashierBizParams, rest } }
        })

        this.data.payChannel.requestPayment(sqbBridge)(wapPayRequest, { cashierBizParams, ...rest })
      }
    }
  }
}

function handleOrderOrder(this) {
  return ({ data = <{ pay_way: TPayway }>{} }) => {
    if (data.pay_way === 101) {
      this.$loading('储值支付中...')
    }
    if (data.pay_way === 22) {
      this.$loading('索迪斯支付中...')
    }
  }
}

// 获取配送费
async function fetchDeliverFee(this, opts = {}) {
  const type = _.get(opts, 'type')
  const {
    defaultAddress,
    addressDisabled,
    addressCode,
    addressName,
    presetTimeDict,
    distance,
    errors,
    sqbBridge,
    // @ts-ignore
    deliveryFloorData,
    userDeliveryData
  } = this.data

  // @ts-ignore
  const _ind = _.findIndex(userDeliveryData, item => item.status)
  wx.showLoading({ title: '', mask: true })
  const body = {
    defaultAddress,
    addressDisabled,
    addressCode,
    addressName,
    presetTimeDict,
    distance,
    currentFloor: userDeliveryData[_ind].currentFloor
  }

  if (_.isEmpty(deliveryFloorData)) {
    body.currentFloor = ''
  }

  return this.manager
    .fetchDeliveryFee(body)
    .then(async deliverFee => {
      if (deliverFee) {
        // 比对缓存的配送费与新拉取的配送费是否一致，不一致则展示toast
        if (
          _.get(deliverFee, 'realDeliveryFee') !== _.get(this.data.deliverFee, 'realDeliveryFee')
        ) {
          wx.showToast({
            title: this.t('由于配送时间或地址变化，您的配送费也发生了变化'),
            icon: 'none'
          })
        }
        // 更新errors， 否则无法下单
        const idx = _.findIndex(errors, { code: DELIVERY_FEE_ERROR_CODE })
        if (idx > -1) {
          errors.splice(idx, 1)
        }
        const amountCompositionData = _.pick(deliverFee, ['amountComposition', 'tradeApp'])
        const materialChangeData = _.get(opts, 'materialChangeData')

        const discountParams = {}

        if (!materialChangeData) {
          this.setData({
            errors,
            deliverFee,
            realDeliveryFee: deliverFee.realDeliveryFee,
            currentFloor: body.currentFloor,
            ...amountCompositionData
          })
        } else {
          _.assign(discountParams, {
            from: 'take_out_order.material.change',
            amountCompositionData,
            materialChangeData
          })
        }
        await this.fetchDiscount(false, discountParams)
      }
    })
    .catch(async (error: any) => {
      console.error(error)
      const errMsg = _.get(error, 'message')
      const errCode = _.get(error, 'code')
      // 50015:未选择必选品,此异常码执行回调的时候去点单首页
      if (errCode === '50015' && type === 'beforeSubmit') {
        this.alert(
          errMsg,
          () => {
            this.closeAlert()
            this.redirect('page-home', { fromPaySubmitOrder: 'batchNoMustOrderGoods' })
          },
          '去选择'
        )
      } else if (errMsg) {
        wx.showToast({
          title: errMsg,
          icon: 'none'
        })
      }
    })
    .finally(() => wx.hideLoading())
}

function fetchDefaultAddressV2(this, isBookTimeChange: Boolean = false) {
  const { presetTimeDict, serviceTypeName } = this.data
  let { userDeliveryData } = this.data
  const presetTime = presetTimeDict[serviceTypeName] || -1
  const body = {
    presetTime
  }
  return this.manager
    .fetchDefaultAddressV2(body)
    .then((defaultAddress: TSubmit.TSubmitPayload['defaultAddress']) => {
      defaultAddress = defaultAddress || {}
      // 默认地址如果为商家自定义指定配送地址，addressName变为空防止出现address和addressName同时为地址导致展示重复问题
      if (_.get(defaultAddress, 'addressId', 0) > 0) {
        // @ts-ignore
        defaultAddress.addressName = ''
      }
      const {
        addressCode,
        addressName,
        id: currentAddressId,
        valid,
        distance,
        floors: deliveryFloorData = [],
        // @ts-ignore
        preChooseFloor
      } = defaultAddress as TSubmit.TCamelAddress

      if (!isBookTimeChange) {
        userDeliveryData = this.updateUserDeliveryData(preChooseFloor, deliveryFloorData, true)
      }

      this.setData({
        defaultAddress,
        currentAddressId,
        addressCode,
        addressName,
        addressDisabled: !valid,
        distance,
        deliveryFloorData,
        userDeliveryData
      })
      return defaultAddress
    })
    .catch(() => ({}))
}

async function fetchCart(this, setFlag = true): Promise<TSubmit.TCart> {
  return await this.manager.fetchCart(setFlag, originCart => {
    // const originalPrice = _.get(originCart, 'total_price', 0);
    this.setData({ originCart, cart: _.cloneDeep(originCart) })
  })
}

function onSubscribeOrderTypeChange(this) {
  return type => {
    const { packType } = this.data
    type !== packType && this.initPayload({ order_type: 'subscribe_order', packType: type })
  }
}

function onPaytypeChange(this: Submit) {
  // @ts-ignore
  this.debouncedFetchDiscount =
    // @ts-ignore
    this.debouncedFetchDiscount ||
    _.debounce((ctx, payway) => {
      const orderModel = ctx.manager.orderSvc.orderModel
      ctx.manager.callAsync('order.payway_changed', { payway: payway, orderModel }, () =>
        ctx.fetchDiscount()
      )
    }, 10)

  return data => {
    const {
      isSelected,
      description,
      buttonText,
      amountDescription,
      amount,
      paytype,
      payway,
      isNeedReloadDiscount = false,
      extra = {}
      // ==== 统一收银台返回数据
    } = data
    ECHO('onPaytypeChange', data)
    const oldPayway = this.data.payway
    const params = <{ payway: number; paytype: string }>{}

    if (payway !== oldPayway) {
      emenuChannel.setMkCustomInfo({ previous_pay_way: oldPayway })
    }

    // if (!_.isEmpty(extra)) {
    //   const { interestId } = extra;
    //   this.interestId = interestId;
    // }

    if (isSelected) {
      params.payway = payway
      params.paytype = paytype
    }

    if (paytype === CONSTANT.STORED_PAY_TYPE && extra) {
      // 储值卡支付
      this.setData({ isNativePay: false, ...params })
    } else if (paytype === CONSTANT.SODEXO_PAY_TYPE) {
      // 索迪斯支付
      this.setData({
        isNativePay: !isSelected,
        customAmount: amount,
        amountDescription,
        description,
        buttonText,
        ...params
      })
    } else {
      // 微信 支付宝支付
      // 当为微信支付宝支付时，需要将储值并支付(isNeedStoredRecharge)置为false
      this.setData({ isNativePay: true, isNeedStoredRecharge: false, ...params })
    }

    if (payway !== oldPayway || isNeedReloadDiscount) {
      // @ts-ignore
      this.debouncedFetchDiscount(this, payway)
    }
  }
}

function onTakeoutOrderTypeChange(this: Submit) {
  return value => {
    const { storeMerchantInfo, takeoutProfitSharing, supportCardPay } = this.data
    // storeMerchantInfo.paymentScene = getPaymentScene(value);
    storeMerchantInfo.bizScene = getPaymentScene(value, takeoutProfitSharing, supportCardPay)
    this.setData({ serviceTypeName: value, storeMerchantInfo: _.cloneDeep(storeMerchantInfo) })
    this.initPayload({ order_type: value })
    this.initExtra()
    // 外卖自取切换的时候需要重新埋点曝光事件
    this.$track('SmMpPageDisplay', {})
  }
}

function onAddressChange(this: Submit) {
  return async addressId => {
    // 选其他地址时
    if (addressId) {
      // wx.showLoading({ title: '加载中...' });
      const defaultAddress = this.data.addressList.filter(item => item.id === addressId)[0]
      this.update({ currentAddressId: addressId, defaultAddress })
      // 设置默认地址
      await this.manager.updateDefaultAddress(addressId)
      this.onCloseSelectAddress()
    }

    // 新增地址时
    this.manager
      .fetchAllAddress()
      .then(addressList => this.update({ addressList, isBookTimeChange: false }))

    fetchDefaultAddressV2
      .call(this)
      .then(() => {
        return fetchDeliverFee.call(this)
      })
      .then(() => this.$clearToast())
      .catch(err => {
        console.error(err)
        this.$clearToast()
      })
  }
}

function onBooktimeChange(this: Submit) {
  return detail => {
    // eslint-disable-next-line prefer-const
    let { timerIdexMap, serviceTypeName, isRetail } = this.data
    const { dayIndex, timeIndex } = detail
    timerIdexMap = {
      ...timerIdexMap,
      [serviceTypeName]: {
        dayIndex,
        timeIndex
      }
    }
    this.setData(
      {
        dayIndex,
        timeIndex,
        damBoard: false,
        timerIdexMap
      },
      () => {
        this.setData({ selectTimeSheetShow: false })
        const { presetTimeDict, timerIdexMap, serviceTypeName, bookTimes } = this.data

        this.manager.updateCurrentTime(
          { presetTimeDict, timerIdexMap, bookTimes, isRetail },
          data => {
            this.setData(data)
            // 因为外卖的时间可能会变化，所以需要重新获取配送费
            // 配送时间变化， 要查询地址，确认是否可以配送上楼
            // addressCode 不为空时，说明为校园外卖地址
            const { addressCode } = this.data
            if (addressCode && serviceTypeName === CONSTANT.TAKE_OUT_ORDER) {
              fetchDefaultAddressV2.call(this, true).then(() => fetchDeliverFee.call(this))
            } else {
              // 如果不需要执行fetchDefaultAddressV2，直接执行fetchDeliverFee
              serviceTypeName === CONSTANT.TAKE_OUT_ORDER && fetchDeliverFee.call(this)
            }
          }
        )
      }
    )
  }
}

/**
 * onSubsidyChange函数处理加价购商品的变化。
 *
 * 流程图：
 *
 * 开始
 *  |
 *  |--> 获取商品信息和操作类型（增加或减少）
 *  |
 *  |--> 获取当前的品牌活动和购物车信息
 *  |
 *  |--> 显示加载中的提示
 *  |
 *  |--> 构造请求体，包含服务类型、商品信息和品牌活动产品信息
 *  |
 *  |--> 如果是减少操作，需要找到购物车中的商品项，并将其id添加到请求体中
 *  |
 *  |--> 尝试调用manager的goodsSubsidyAndRedeem方法处理加价购
 *  |     |
 *  |     |--> 如果成功，重新获取购物车信息，更新品牌活动的商品数量，重新获取折扣
 *  |     |
 *  |     |--> 如果失败，打印错误信息，隐藏加载中的提示
 *  |
 * 结束
 *
 * @returns {Function} 返回一个异步函数，该函数接收一个参数，包含商品信息和操作类型（增加或减少）
 */
export function onSubsidyChange(this) {
  return async params => {
    const { goods = {}, type } = params
    const { brandActivity = {}, cart } = this.data

    const body = {
      service_type: this.serviceType,
      item: {
        number: type === 'add' ? 1 : -1,
        id: goods.id,
        price: goods.price,
        name: goods.name
      },
      brand_act_product: {
        activity_id: brandActivity.activity_id,
        product_id: goods.product_id
      }
    }

    if (type === 'minus') {
      const cart_item = findCartRecordByGoodsId(goods.id, cart)
      if (cart_item) {
        // @ts-ignore
        body.item_uid = cart_item.id
      }
    }

    try {
      await this.manager.goodsSubsidyAndRedeem(body)
      await fetchCart.call(this)
      const _brandActivity = updateSubsidyGoodsNumber(this.data.cart, brandActivity)
      this.setData({ brandActivity: _.cloneDeep(_brandActivity) })
      await this.fetchDiscount(true, { from: 'goods_subsidy.change' })
      this.$clearToast()
    } catch (error) {
      console.error(error)
      this.$toast(error || '加购错误')
      this.$clearToast()
    }
  }
}

/**
 * 此函数从提供的购物车对象中通过其id查找购物车记录。
 * 它过滤出购物车中标记为 brand_act 的记录，然后找到具有提供的id的记录。
 *
 * @param {number} id - 购物车记录中的商品id。
 * @param {Object} [cart={}] - 包含记录数组的购物车对象。每个记录代表购物车中的一个商品，具有 item_id、num 和 brand_act 属性。
 * @returns {Object} 具有提供的id的购物车记录。如果没有找到记录，返回 undefined。
 */
export function findCartRecordByGoodsId(id, cart = {}) {
  // @ts-ignore
  const { records = [] } = cart
  const brandRecords = records.filter(r => r.brand_act)

  return _.find(brandRecords, { item_id: id })
}

/**
 * 此函数根据购物车记录更新 brandActivity 对象中每个项目的 activity_number。
 * 它遍历 brandActivity 对象中的每个项目，并检查购物车记录中是否有对应的记录。
 * 如果找到对应的记录并且标记为 brand_act，则将项目的 activity_number 更新为该记录的数量。
 * 如果没有找到对应的记录或者记录未标记为 brand_act，则将项目的 activity_number 设置为 0。
 *
 * @param {Object} [cart={}] - 包含记录数组的购物车对象。每个记录代表购物车中的一个项目，具有 item_id、num 和 brand_act 属性。
 * @param {Object} [brandActivity={}] - 包含项目数组的 brandActivity 对象。每个项目代表一个产品，具有 id 和 activity_number 属性。
 * @returns {Object} 更新后的 brandActivity 对象。
 */
export function updateSubsidyGoodsNumber(cart = {}, brandActivity = {}) {
  // @ts-ignore
  const { records = [] } = cart
  // @ts-ignore
  const { items = [] } = brandActivity

  const brandRecords = records.filter(r => r.brand_act)

  for (const item of items) {
    const index = _.findIndex(brandRecords, { item_id: item.id })
    if (index !== -1) {
      item.activity_number = brandRecords[index].num
    } else {
      item.activity_number = 0
    }
  }

  return brandActivity
}

function onMaterialChange(this) {
  return params => {
    const { cart, paidAmount, originalPrice, serviceTypeName } = this.data
    this.$loading('')
    this.manager
      .handleMaterialChange(params, cart, paidAmount, originalPrice)
      .then(data => {
        return data
      })
      .then(materialChangeData => {
        return this.manager.transformMaterialData(() => {
          this.setSubmitButtonStatus(true)
          this.manager
            .addMaterialAndRedeem(async (err, data) => {
              // @ts-ignore
              data.cart = materialChangeData.cart
              if (err) {
                data.originCart = (await fetchCart.call(this, false)) || {}
              }
              this.setData(data)
              if (serviceTypeName === CONSTANT.TAKE_OUT_ORDER) {
                await fetchDeliverFee.call(this, { materialChangeData })
              } else {
                await this.fetchDiscount(true, { from: 'material.change' })
              }
              this.$clearToast()
            })
            .catch(err => {
              console.error(err)
              this.$clearToast()
            })
            .finally(() => this.setSubmitButtonStatus(false))
        })
      })
  }
}

function onCartUserChange(this) {
  return count => {
    this.manager.updateUserCount(count).then(async data => {
      this.setData(data)
      await fetchCart.call(this)
      await this.fetchDiscount(true, { from: 'user.change' })
    })
  }
}

function onClickCurrentAddress(this) {
  return exist => {
    const { addressList, sqbBridge } = this.data

    // 如果已有地址列表，直接显示，否则到新增地址页面
    if (exist && _.size(addressList)) {
      return this.setData({ selectAddressSheetShow: true })
    } else {
      return this.manager.fetchAllAddress().then(addressList => {
        this.setData({ addressList }, () => {
          // NOTE:
          // area应该从submit payload 返回
          // 首页聚合接口应该只在首页调用
          sqbBridge.getPayload(this.storeId).then(payload => {
            // @ts-ignore
            const { areas } = payload
            // @ts-ignore
            const existMerchantAddress = _.find(addressList, item => item.addressId > 0)

            if (existMerchantAddress === void 0 && _.get(areas, 'type') === 'address')
              this.go('page-address')
            else this.setData({ selectAddressSheetShow: true })
          })
        })
      })
    }
  }
}

// @ts-ignore
async function slsShowStoredPay(sqbBridge, val: TSubmit.TMKSSStatusReturns) {
  try {
    const takeoutProfitSharing = await sqbBridge.getMcc(CONSTANT.MCC_TAKEOUT_PROFIT_SHARING)
    sqbBridge.sls('INFO', {
      type: 'showStoredPay',
      data: {
        ...val,
        takeoutProfitSharing,
        serviceTypeName: this.manager.serviceTypeName
      }
    })
  } catch (err) {
    console.error(err)
  }
}

function onFloorChange(this) {
  return floor => {
    fetchDeliverFee.call(this)
  }
}

// 统一收银台监听事件
function setupPayPluginListeners(this) {
  const { sqbBridge } = this.data
  this.data.payChannel.on('onPayToolChange', e => {
    // @ts-ignore
    sqbBridge.sls('INFO', { type: 'cashier-on-onPayToolChange', data: { data: e.data } })
    const usingPayTools = []
    // @ts-ignore
    const cashierPayTools = e.data
    if (_.isArray(cashierPayTools)) {
      // usingPayTools = _.reduce(
      //   cashierPayTools,
      //   (curr, { amount, code, amountComposition }) => {
      //     curr.amount = amount;
      //     curr.payTool = code;
      //     curr.amountComposition = amountComposition;
      //     return curr;
      //   },
      //   { payTool: '', amount: 0, amountComposition: [] },
      // );

      _.forEach(cashierPayTools, item => {
        usingPayTools.push({
          payTool: item.code,
          amount: item.amount,
          amountComposition: item.amountComposition
        })
      })
    }

    // @ts-ignore
    this.setData({ cashierPayTools, usingPayTools })
    let data = {}
    // @ts-ignore
    if (_.isArray(e.data)) {
      // @ts-ignore
      const payway = _.get(e.data, '0.code')
      // @ts-ignore
      const prepay = _.get(e.data, '0.prepay')
      const paytype = payway == 105 || prepay ? CONSTANT.STORED_PAY_TYPE : getTerminalName()
      // @ts-ignore
      this.__payway = payway
      // @ts-ignore
      this.__paytype = paytype

      data = {
        isSelected: true,
        paytype: paytype,
        payway: payway,
        isNeedReloadDiscount: true
      }
    }
    // @ts-ignore
    ECHO('onPayToolChange', e.data)
    this.onPaytypeChange({ detail: data })
  })

  this.data.payChannel.on('onPayPluginPrepayChange', (e: { data: any }) => {
    const prepayData = _.get(e, 'data.prepay')
    const payToolData = _.get(e, 'data.payTool')

    ECHO('setPrepayRechargeAmount', prepayData)
    sqbBridge.sls('INFO', {
      type: 'cashier-on-setPrepayRechargeAmount',
      data: { data: { prepayData, payToolData } }
    })

    // 设置优惠接口请求参数
    emenuChannel.setMkCustomInfo(_.get(prepayData, 'mk_custom_info', {}))

    // ! -------------- 测试
    const {
      depositAmountLevel,
      depositAmountLevelDescription,
      // 权益 ID
      interestId,
      // 是否需要重新拉取优惠
      isNeedReloadDiscount,
      // 所选储值规则赠送的商品抵用券数量
      couponGoodsCount = 0,
      ...payload
    } = prepayData || {}
    this.interestId = interestId
    this.setData({
      customAmount: depositAmountLevel,
      amountDescription: depositAmountLevelDescription,
      goodsCouponCount: ~~_.get(this.getQuery(), 'goodsCouponCount', 0) + couponGoodsCount,
      ...payload
    })

    let data = {}

    if (_.isObject(payToolData)) {
      // @ts-ignore
      // const payway = _.get(payToolData, 'code');
      // @ts-ignore
      // const prepay = _.get(payToolData, 'prepay');
      // const paytype = payway == 105 || prepay ? CONSTANT.STORED_PAY_TYPE : getTerminalName();

      // onPayPluginPrepayChange 忽略payway， 一切onPayToolChange为准
      data = {
        isSelected: true,
        // @ts-ignore
        paytype: this.__paytype,
        // @ts-ignore
        payway: this.__payway,
        isNeedReloadDiscount: true
      }

      this.onPaytypeChange({ detail: data })
    }

    // if (isNeedReloadDiscount) {
    // 使用定时器,避免setPrepayRechargeAmount触发时onPayToolChange未触发导致优惠计算错误
    // setTimeout(this.fetchDiscount);
    // }
  })

  this.data.payChannel.on('submit', e => {
    // @ts-ignore
    sqbBridge.sls('INFO', { type: 'cashier-on-submit', data: { data: e.data } })
    // @ts-ignore
    const { amount, cashierBizParams, paying } = e.data
    // @ts-ignore
    ECHO('submit', e.data)
    // 提交业务订单
    // 业务订单成功之后会返回支付参数，调用下面的`requestPayment`方法来进行实际支付
    if (!paying) {
      this.setData({ cashierBizParams, amount })
      this.onPay()
    }
  })

  this.data.payChannel.on('onPayFinish', e => {
    const client_sn = this.data.client_sn
    const order = { client_sn, data: { client_sn } } // 主要兼容收银台的支付失败
    ECHO('onPayFinish', e)
    const payResult = _.get(e, 'data.payResult')
    // @ts-ignore
    sqbBridge.sls('INFO', { type: 'cashier-on-onPayFinish', data: { data: e.data } })

    if (payResult === 'success') {
      // @ts-ignore
      this.manager.call(`order.pay_${payResult}`, null, order)
    } else {
      this.manager.call(`order.pay_${payResult}`, order)
    }
  })
}
