/* prettier-ignore */
@grid_cate_item_height: 34px;
/* prettier-ignore */
@grid_gap: 7PX;

.smart-grid() {
  &.grid-mode {
    .page {
      &.with-skeleton {
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="290"><image x="0" y="0" preserveAspectRatio="xMinYMin meet" width="96%" height="100%" xlink:href="data:image/webp;base64,UklGRqQCAABXRUJQVlA4IJgCAAAQRgCdASpdAR0CPm02l0kkIyIiIRFpaIANiWlu4XVRG/PEa20CN/xpXIvkuRfJb9+6X4Uy6peNK5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci9cRot5d3rLP4Syz+Ess/hLLP4Syz+Ess/hLLJIULP9cru3bt2n1gdOZhb/jSuRfJV8Q4Ab7NbgpAi+S5F8lwCmYWepTFO/BRhPwTdXTf8aVyL5KjT0xcOMxbC+HTLNOmWadMs06ZZp0pmH3uweOuaXQ8VEJ6wsw+Myg8dc0uh4qIT1hZh8ZlA6ue8sMTgBwNcdGuqXjRzgwvjWuqXjSuRfJcgY0qjse24J48ePHjx48ePHjx48ePHjx34N/xpXIvkuRfJciysIY0GOxpDZSo4uOrzeuvXr2GwTlAJsjc6fI3ObzCN76uWqXBkxYFwK3vVyL484JRquApPGg7Ofv3ua1J4lGnoDCbWF2c/fv3794kknx2hqJNYJFsiLZEWyItkRbIi2RFsiLZEWyItkACEqrTBoAD+/42pFRmldUOJ3OUlHur02GE2otD54lMbWZigTyGxmyAbSa82VTVHAr7smbKH+HkfvSQXDhM0wu1bxv09Nk8o5vBjdGAeo3X1wjOfPXM5dvEfFN7wmtE6tWfvVBgA" /></svg>'),
          url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="290"><image x="0" y="0" preserveAspectRatio="xMaxYMin meet" width="96%" height="100%" xlink:href="data:image/webp;base64,UklGRqQCAABXRUJQVlA4IJgCAAAQRgCdASpdAR0CPm02l0kkIyIiIRFpaIANiWlu4XVRG/PEa20CN/xpXIvkuRfJb9+6X4Uy6peNK5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci+S5F8lyL5LkXyXIvkuRfJci9cRot5d3rLP4Syz+Ess/hLLP4Syz+Ess/hLLJIULP9cru3bt2n1gdOZhb/jSuRfJV8Q4Ab7NbgpAi+S5F8lwCmYWepTFO/BRhPwTdXTf8aVyL5KjT0xcOMxbC+HTLNOmWadMs06ZZp0pmH3uweOuaXQ8VEJ6wsw+Myg8dc0uh4qIT1hZh8ZlA6ue8sMTgBwNcdGuqXjRzgwvjWuqXjSuRfJcgY0qjse24J48ePHjx48ePHjx48ePHjx34N/xpXIvkuRfJciysIY0GOxpDZSo4uOrzeuvXr2GwTlAJsjc6fI3ObzCN76uWqXBkxYFwK3vVyL484JRquApPGg7Ofv3ua1J4lGnoDCbWF2c/fv3794kknx2hqJNYJFsiLZEWyItkRbIi2RFsiLZEWyItkACEqrTBoAD+/42pFRmldUOJ3OUlHur02GE2otD54lMbWZigTyGxmyAbSa82VTVHAr7smbKH+HkfvSQXDhM0wu1bxv09Nk8o5vBjdGAeo3X1wjOfPXM5dvEfFN7wmtE6tWfvVBgA" /></svg>');
        background-size: 50% 580px, 50% 580px;
        background-repeat: repeat-y, repeat-y;
        background-position: left top, right top;
      }
    }
    //.scroll-content-wrap {
    .good-wrap {
      .categories-scroll-view {
        z-index: 100;
        /* prettier-ignore */
        /* prettier-ignore */
        height: 48PX;
        /* prettier-ignore */
        /* prettier-ignore */
        min-height: 48PX;
        /* prettier-ignore */
        /* prettier-ignore */
        line-height: 48PX;
        width: 100%;
        padding-bottom: 0;
        background-color: #fff !important;

        .category-item {
          min-height: auto;
          padding: 0;
          /* prettier-ignore */
          /* prettier-ignore */
          flex: auto 1 0;
          /* prettier-ignore */
          /* prettier-ignore */
          padding-right: 50px;
          background-color: #fff !important;

          .category-name-text {
            /* prettier-ignore */
            /* prettier-ignore */
            // padding-left: 5PX;
          }

          &-icon {
            --image-width: 28px;
            --image-height: 28px;
          }

          &.selected {
            font-size: 36px;
            font-weight: bold;
            color: var(--primary__color);
          }

          &:first-child {
            /* prettier-ignore */
            /* prettier-ignore */
            // padding-left: 30px;
          }

          &:last-child {
            /* prettier-ignore */
            /* prettier-ignore */
            // padding-right: 280PX;
          }
        }

        &--decorator {
          width: 100%;
          position: sticky;
          z-index: 900;
          pointer-events: none;

          &::after {
            content: '';
            position: absolute;
            /* prettier-ignore */
            /* prettier-ignore */
            height: 48PX;
            /* prettier-ignore */
            /* prettier-ignore */
            top: -48PX;
            right: 0;
            width: 40px;
            background: linear-gradient(
              270deg,
              #ffffff 0%,
              #ffffff 51%,
              rgba(255, 255, 255, 0) 100%
            );
            opacity: 0.95;
          }
        }
      }

      .category-group {
        background: transparent !important;
      }

      .category-name {
        height: @grid_cate_item_height !important;
        line-height: @grid_cate_item_height !important;
        padding: 30px 0 20px !important;
        top: 0 !important;
        background-color: transparent !important;
        background: transparent !important;
        box-sizing: content-box;
        // @prettier-ignore
        /* prettier-ignore */
        // margin: 10PX 0;
        .category-text {
          // padding: 0 12px;
          /* prettier-ignore */
          // margin: 10PX 0;
          &-name {
            display: inline-block;
            line-height: @grid_cate_item_height;
          }

          .must-select-tip {
            &--card {
              color: var(--primary-font-color);
              height: 29px;
              font-size: 19px;
              background: var(--primary-color);
              padding: 0 11px;
              border-radius: 14px 14px 14px 1px;
              box-sizing: border-box;
              margin-left: 4px;
            }
          }
        }

        &--card {
          height: @grid_cate_item_height;
          line-height: @grid_cate_item_height;
          background-color: #f7f7f7 !important;
        }

        &.same-prev {
          height: 0 !important;
          line-height: 0 !important;
          padding: 0 !important;
          margin-top: @grid_gap;

          .category-container {
            display: none;
          }
        }

        .category-container {
          border-radius: 4px;
          font-weight: 400;
        }
      }

      .home-goods-list--decorator {
        // @prettier-ignore
        /* prettier-ignore */
        height: 15PX;
        background: transparent;
        border-radius: 20px 20px 0px 0px;
        position: sticky;
        z-index: 900;
      }

      .grid-goods-list {
        width: 100%;
        //display: grid !important;
        //grid-template-columns: minmax(0, 1fr) minmax(0, 1fr);
        grid-gap: 20px;
        column-count: 2;
        column-gap: 12px;
        background-color: #f7f7f7;
        // TODO: 骨架屏，网格要实现
        background-image: none !important;

        .goods-item-class {
          // 设置max-height
          //--goods-height: 539px;
          /* prettier-ignore */
          //--gift-card-goods-height: 294PX;
          height: 100%;
          min-height: 454px;
          max-height: 504px;
          --goods-sales-info-layout: column;
          --goods-sales-info-justify: space-between;
          --goods-title-max-width: var(--goods-width);
          --goods-content-padding: 0 12px 20px 12px;
          --goods-title-line-clamp: 1;
          --goods-item-row-gap: 12px;
          --title-font-size: 30px;
          --goods-image-width: 100%;
          --goods-image-height: 266px;
          --font-size: 30px;
          --goods-item-tips-font-size: 30px;
          --goods-title-margin-top: 0;
          --goods-image-radius: 16px 16px 0 0;
          --goods-footer-price-font-size: 32px;
          --counter-icon-size: 40px;
          background-color: #fff;
          //border: 1px solid #e0e0e0;
          border-radius: 16px;
          box-sizing: content-box;
        }

        .goods-badge-class {
          --badge-icon-border-width: 1px;
        }

        .goods-card-box {
          // max-height: 541px;
          // width: 40vw!important;
        }

        .grid-goods-price {
          &::before {
            font-size: 24px;
          }
        }

        .goods-price-out-of-stock {
          --goods-item-footer-price-color: #999;
        }
      }
    }

    // end .good-wrap
    //}

    .end-text {
      background-color: #f7f7f7 !important;
    }
  }
}
