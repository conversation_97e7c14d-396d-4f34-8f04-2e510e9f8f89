/* prettier-ignore */
@cate_item_height: 36PX;

.smart-goods-list() {
  --goods-split-tag-text-color: var(--primary__color, #ff6a16);
  --goods-split-tag-bg-color: var(--primary__color, #ff6a16);

  .page {
    // GPU加速方案
    //position: relative;
    //transform: translateZ(0);
    //backface-visibility: hidden;
    //will-change: transform;
    //transition: height 0.1s ease;
    width: 100%;

    &.list-skeleton {
      //transition: background-image 0.3s ease-in-out, background-color 0.3s ease-in-out;
      //animation: wave1 1.6s infinite;
    }
  }

  &.list-mode {
    .page {
      //background-image: url('https://smart-static.wosaimg.com/theme_mp/goods-list-skeleton.webp');:image/png;base64,
      background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="112"><image x="0" y="0" preserveAspectRatio="xMinYMin meet" width="100%" height="100%" xlink:href="data:image/webp;base64,UklGRsABAABXRUJQVlA4ILQBAADQLwCdASomAtoAPm02lkikIyKhIlB6SIANiWlu4XVRG/PEbdcCZgbuIrqWv9edS1/rzqWv9edSpYcgksyexIcEtvcMmAeZxkwDzOMmAeV9FJTQCWv9aimKS3lQ+rlY66Ph8pbi+MQeZxkwDzOIHR5nGShIVROMlU8OT8zjJgHmcZL59uMmAdqC+uYtY+PTujMLgnw72F1/rzqWv8nYXX+vIvUHxjsqjro+Pj07o0fXpJzQGwXX+vI2SYB5meG3Opa/151LX3OkLr/W031/rzlAB9x9Uc0fXpKaR8fHp3Ro9J+fVGs6lr/J2F1/ryNl7eQIfTHhHXAh4pSy6rlFmAeZxAOiibFZvf2Guj4+PTujR9ekppHx8enCNAeklOdS1/k1TFl1XUtf686lr/Wt1vYCjDgfcePj07o0fXIAui9LweG6sDAYRXpKaR8fHp3Ro+vST+TyLRiDzOMmAeZxkwDzOMmAeZxkvac/YZz9hnP2Gc/YZz9hnP2Gc/YZz9hnP2Gc/YZz9hnAAP7/o10i0sScSwPv3dxMvfwtkzejZpuRmA26k73O0S5lvVOIC0wj2aaKEAAA" /></svg>');
      background-size: 100% 224px;
      background-repeat: repeat-y;
      /* prettier-ignore */
      background-position: 1PX top;
      background-color: transparent;
    }
  }
  .goods-list {
    // border: 1px solid blue;
    z-index: 98;
    height: auto;
    position: relative;
    padding-bottom: calc(env(safe-area-inset-bottom) + 124px + 80px);

    .goods-desc {
      max-width: 350px;
    }

    .single-hot-sale-tag-text {
      font-size: var(--goods-item-single-hot-sale-tag-text-font-size, 20px);
      line-height: 1;
    }

    .single-hot-sale-tag-class {
      --goods-item-single-hot-sale-tag-text-font-size: 22px;
      --tag-default-color: var(--goods-item-single-hot-sale-tag-default-color, #fff2eb);
      color: var(--goods-item-single-hot-sale-tag-color, #ff6a16);

      &::before {
        content: '';
        display: block;
        /* prettier-ignore */
        width: 11PX;
        height: 100%;
        background: url('https://smart-static.wosaimg.com/themes/default/hotsale_medal.webp')
          no-repeat;
        background-size: contain;
        background-position: center center;
      }
    }

    .goods-detail-sale-count {
      --info-color: #999;
    }

    .split-tag-class {
      --split-tag-text-color: var(--goods-split-tag-text-color);
      --split-tag-bg-color: var(--goods-split-tag-bg-color);
    }

    .gift-card-tag-class {
      --split-tag-label-inner-bg: #fff;
      --split-tag-value-inner-bg: #333;
      --split-tag-label-bg: #fff;
      --split-tag-value-bg: #333;
      --split-tag-label-text-color: #333;
      --split-tag-value-text-color: #fff;
      --split-tag-border-color: #333;
      border: 1px solid #333;
      --split-tag-border-radius: 8px;
    }

    .category-group-view {
      width: 100%;
      height: 1px;
    }

    .category-group-observe-view {
      width: 100%;
      height: 1px;
      background-color: transparent;
      position: sticky;
      z-index: 9;
    }

    .retail-goods-musk {
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      position: absolute;
      left: 0;
      top: 0;
      z-index: 10;
    }

    .sub-category-wrap {
      width: 100%;
      padding-top: 20px;
      position: sticky;
      background-color: #fff;
      z-index: 11;

      .sub-category-scroll-view {
        padding-left: 10px;
        overflow: auto;
      }

      .sub-category-items {
        margin-bottom: 16px;

        .sub-category-item {
          min-width: 80px;
          color: #666;
          font-size: 24px;
          height: 44px;
          line-height: 44px;
          text-align: center;
          padding: 0 10px;
          margin-right: 16px;
          background: #f5f5f5;
          border-radius: 8px;
          box-sizing: border-box;
          overflow: hidden;
          transform: rotateZ(360deg);
        }

        .active-sub-category {
          color: var(--primary__color);
          border: 1px solid var(--primary__color);
          background: var(--primary-selected__color);
        }
      }

      .sub-inline-items {
        display: -webkit-inline-box;
        .sub-category-item {
          white-space: nowrap;
        }
      }

      .more-icon {
        width: 46px;
        height: 40px;
        position: relative;
        top: 4px;
        box-shadow: -2px 0px 4px -2px rgba(0, 0, 0, 0.07);

        .category-icon {
          width: 18px;
          height: 12px;
          display: block;
        }

        .down-category-icon {
          transform: rotate(180deg);
          transform-origin: center;
        }
      }

      .all-sub-category {
        width: 100%;
        padding: 0 10px 11px 20px;
        margin-top: -1px;
        margin-bottom: 0;
        background-color: #fff;
        border-radius: 0px 0px 8px 8px;
        position: absolute;
        left: 0;
        box-sizing: border-box;

        .sub-category-item {
          font-size: 24px;
          width: 157px;
          height: 50px;
          line-height: 50px;
          padding: 0;
          word-break: break-all;
          margin-right: 19px;
          margin-bottom: 19px;
          overflow: hidden;

          &:nth-of-type(3n) {
            margin-right: 0;
          }
        }
      }
    }

    // 商品样式
    .goods-item-class {
      /* prettier-ignore */
      --goods-item-min-height: 81PX;
      /* prettier-ignore */
      --goods-item-min-width: 81PX;
      /* prettier-ignore */
      padding: 1rpx 10PX var(--goods-item-padding-bottom) 10PX;
      min-height: var(--goods-item-min-height);
      --goods-item-image-width: var(--goods-item-width);
      --goods-image-height: var(--goods-item-min-height);
      --goods-image-width: var(--goods-item-min-width);
    }

    .category-skeleton-wrap {
      width: 200px;
      background: #f6f6f6;
      z-index: 1;
    }

    .goods-wrap {
      //display: flex;
      overflow: visible;
      flex-direction: column;
      // 解决ios低版本sticky因设置z-index而不显示的问题
      transform: translate3d(0, 0, 0);
      contain: layout;
    }

    .with-bg-color {
      background-image: none;
      background-color: #fff;
    }

    .category-group {
      background-color: transparent;
      transition: background-image 0.3s ease-in-out, background-color 0.3s ease-in-out;
      &.with-bg {
        background-image: none;
        background-color: #fff;
      }

      .category-name {
        /* prettier-ignore */
        padding: 0 10PX;
        height: @cate_item_height;
        line-height: @cate_item_height;
        font-size: 26px;
        font-weight: bolder;
        color: #666;
        /* prettier-ignore */
        top: 87PX;
        z-index: 9;
        transition: height 0.1s ease-in;
        transform: translate(0, 0, 0);
        background: #fff;

        &.same-prev {
          height: 0;
          line-height: 0;
        }

        .category-container {
          width: 0;
          flex: 1;
          height: inherit;

          .category-tag-icon {
            background-size: contain;
            background-repeat: no-repeat;
            /* prettier-ignore */
            height: 18PX;
            width: 100%;

            &.hotSale {
              background-image: var(--home-category-hotsale-tag-icon-url);
            }
            &.recommend {
              background-image: var(--home-category-recommend-tag-icon-url);
            }
          }
        }

        &.sticky {
          /* @minipack-ignore */
          position: sticky;
        }

        &.show {
          /* @minipack-ignore */
          overflow: initial;
          width: 100%;

          .category-container {
            position: absolute;
            top: 0;
            width: 100%;
            height: @cate_item_height;
            background: #fff;
            margin-left: -4px;
          }
        }

        .category-text {
          // width: 0;
          position: relative;
          display: inline-flex;
          height: inherit;

          &-name {
            display: inline-block;
            line-height: @cate_item_height;
          }

          &-pic {
            width: 202px;
            height: 36px;
          }

          .must-select-tip {
            font-size: 22px;
            color: white;
            border-radius: 1000px 1000px 1000px 0;
            background: var(
              --primary-gradient__color,
              linear-gradient(270deg, #f24839 0%, #ff6a16 100%)
            );
            height: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 8px 15px;
            transform-origin: 0 100%;
            margin-left: 16px;
            animation: scale-in-left 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
            box-sizing: content-box;
          }
        }

        .category-tag {
          width: 202px;
          height: 36px;
          background-size: contain;
          background-repeat: no-repeat;
        }

        .category-hotSale {
          background-image: var(--smart-home-category-hot-sale-label__background-image);
        }

        .category-recommend {
          background-image: var(--smart-home-category-recommend-goods-label__background-image);
        }
      }

      &.highlight {
        .goods-wrap {
          background: #fff6e6;
          position: relative;
          overflow: visible;

          &::before,
          &::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 20px;
            left: 0;
            background: #fff;
          }

          &::before {
            background: #fff6e6;
            top: -20px;
          }

          &::after {
            bottom: 0;
          }
        }
      }
    }

    &--card {
      padding: 4px 20px 180px;
      // @prettier-ignore
      /* prettier-ignore */
      margin-top: -10PX;
      background-color: #f7f7f7;
    }
  }
}
