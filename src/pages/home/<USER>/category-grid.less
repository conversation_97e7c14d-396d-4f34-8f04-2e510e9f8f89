/* 网格和零售外面分类样式 */

.smart-category-grid() {
  .retail-category-wrap {
    max-height: 100vh !important;
    /* prettier-ignore */
    padding-bottom: 100PX !important;
    position: sticky;
    top: 0;
    overflow-y: hidden;
    box-sizing: border-box;
  }

  .grid-category-item-badge {
    top: 16px;
    right: 16px;
    position: absolute;
    //--info-size: 34px;
    //--info-font-weight: 500;
    --badge-icon-size-more: 46px;
    --badge-icon-border-radius-more: 16px;
  }

  .category-wrap {
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
      color: transparent;
      display: none;
    }

    contain: layout;
    background: #f6f6f6;
    border-radius: 0 16px 0 0;
    /* prettier-ignore */
    min-height: calc(100vh - 150PX - 124PX - 150PX);
    /* prettier-ignore */
    max-height: calc(100vh - 200PX);
    // overflow: hidden;
    -webkit-overflow-scrolling: touch;
    color: #4a4a4a;
    font-size: 24px;
    width: 200px;
    /* prettier-ignore */
    padding-bottom: 150PX;
    top: 0;

    /*TODO: remove it*/

    .category-item {
      /* prettier-ignore */
      min-height: 48PX;
      position: relative;
      padding: 0 10px;
      text-align: center;
      background: #f6f6f6;

      .category-icon-recent {
        /*css-no-shake*/
        background-image: var(--category-ordered-tag-icon-url);
      }

      .category-icon-hotSale {
        /*css-no-shake*/
        background-image: var(--category-hotsale-tag-icon-url);
      }

      .category-icon-discount {
        /*css-no-shake*/
        background-image: var(--category-discount-tag-icon-url);
      }

      .category-icon-recommend {
        /*css-no-shake*/
        background-image: var(--category-recommend-tag-icon-url);
      }

      &-icon {
        width: 26px;
        height: 26px;
        background-size: contain;
        background-repeat: no-repeat;
      }

      &.selected {
        position: relative;
        color: var(--primary__color);
        background: #fff;
        font-weight: 500;

        & .before {
          pointer-events: none;
          content: '';
          width: 18px;
          height: 18px;
          position: absolute;
          right: 0;
          top: 0;
          background: radial-gradient(circle at left top, transparent 18px, #fff 18px);
          transform: translateY(-18px);
          z-index: 1000;
        }

        & .after {
          z-index: 1000;
          pointer-events: none;
          content: '';
          width: 18px;
          height: 18px;
          position: absolute;
          right: 0;
          bottom: 0;
          background: radial-gradient(circle at left bottom, transparent 18px, #fff 18px);
          transform: translateY(18px);
        }
      }

      &--highlight {
        color: #ff6a16;
      }
    }
  }
}
