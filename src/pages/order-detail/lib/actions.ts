import { callPhone, getQueryOpts, handleHashKey } from '@utils'
import dayjs from '@wosai/emenu-mini-dayjs'
import _ from '@wosai/emenu-mini-lodash'
import { CampusService, CartService } from '@wosai/emenu-mini-services'
import { StorageUtils } from '@wosai/emenu-mini-utils'
import CONFIG from '@wosai/emenu-mini-config'
import { renderOrderStatus } from '@utils/order_detail_action'

const {
  EAT_FIRST_ORDER,
  MEAL_ORDER_CANCELED_STATUS,
  MERCHANT_RECEIVED_ORDER,
  ORDER_FINISH_STATUS,
  RIDER_WORKING,
  ROUND_MEAL,
  ROUND_MEAL_FINISH,
  SUBSCRIBE_ORDER,
  // UN_NORMAL_ORDER,
  WAIT_PAYED_STATUS,
  TAKE_OUT_ORDER,
  PRE_ORDER,
  PAY_FIRST_TABLE_ORDER,
  INIT,
  ACCEPTED,
  NOT_ACCEPTED,
  PAID_AND_ACCEPTED_STATUS
} = CONFIG

// 拼团订单
const GROUP_BUY_ORDER = 'A1'

const { storage } = StorageUtils

const FINAL_STATE = [...MEAL_ORDER_CANCELED_STATUS, ...ORDER_FINISH_STATUS]
const getHighlights = (finalState, status) => {
  const isHighlightInReOrder = _.includes(finalState, status)
  const isHighlightCallMerchant = _.includes(MERCHANT_RECEIVED_ORDER, status)
  const isHighlightCallMan = _.includes([602, 603, 604, 607, 608], status)
  return { isHighlightInReOrder, isHighlightCallMerchant, isHighlightCallMan }
}
const isInStoreOrder = (type, isOnlyInStore) =>
  (type === SUBSCRIBE_ORDER || type === 'PAY_FIRST_ORDER') && isOnlyInStore

const getCustomServiceUrlParams = (order, store, campusStore = false) => {
  const deliveryType = _.get(order, 'extra.dm_info.platform_name')
  const {
    ctime,
    order_seq: orderSeq,
    sn: orderSn,
    store_name: storeName,
    type,
    merchantPhone: storePhone,
    effective_amount: effectiveAmount,
    refund_amount: refundAmount,
    campus_order
  } = order
  const {
    user_name: userName,
    province,
    city,
    district,
    address,
    house_number,
    cellphone: userPhone
  } = _.get(order, 'extra.delivery_info', {}) || {}
  const userAddress = province + city + district + address + house_number
  const { storeSn, merchantSn, merchantName, storeId } = store
  const params = {
    // ? 支付宝不支持 ??, 转为三目运算
    userName: userName ? userName : _.get(order, 'buyer_login'),
    campusStore,
    ctime,
    deliveryType,
    orderSeq,
    orderSn,
    orderStatus: _.get(renderOrderStatus(order), 'statusText'),
    storeName,
    storeSn,
    userAddress,
    orderType: type === TAKE_OUT_ORDER ? '外卖' : '自取',
    userPhone,
    storePhone,
    effectiveAmount,
    refundAmount,
    merchantSn,
    merchantName,
    storeId,
    campusOrder: campus_order
  }
  // 校园外卖增加campusPageId
  if (campusStore) {
    params['campusId'] = _.get(order, 'order_campus_station.campus_id')
  }
  return params
}

export function isLoveFeastOrder(order) {
  const { items = [] } = order
  for (let i = 0; i < items.length; i++) {
    const info = items[i]
    if (info && info.item && info.item.id === 'love_feast') return true
  }

  return false
}

export function getCustomerServiceUrl(
  this: { campusvc: { getCustomerServiceUrl: (params) => Promise<string> } },
  order,
  store,
  isInCampus: boolean
) {
  return this.campusvc
    .getCustomerServiceUrl(getCustomServiceUrlParams(order, store, isInCampus))
    .catch(err => false)
}

/**
 * 通过订单相关数据，构建订单显示按钮数据
 * @param order
 */
const buildActionsByOrder = (order, at) => {
  const {
    merchantPhone,
    isOnlyInStore,
    mealType,
    pay_time,
    process_status,
    type,
    isInCampus,
    pay_controll
  } = order
  let { status } = order

  const pagesMap = {
    ORDER_DETAIL: '订单详情页',
    ORDER_LIST: '订单列表页',
    ORDER_MAP: '地图页'
  }
  // 订单为非正常完成状态
  // 设置订单为完成status = 210
  if (isConvertToFinish(order)) status = 210
  // 是否高亮显示
  const { isHighlightInReOrder, isHighlightCallMerchant, isHighlightCallMan } = getHighlights(
    FINAL_STATE,
    status
  )
  // 是否是店内堂食
  const isMealInStore = isInStoreOrder(type, isOnlyInStore)
  // 是否围餐
  const isRoundMeal = ROUND_MEAL === mealType

  const actions = [
    {
      label: '取消订单',
      weight: 0,
      value: 'cancelOrder',
      visible: false,
      sort: 0,
      iconClassName: 'cancel-order',
      icon: 'iconCancelOrderUrl',
      when(item) {
        return (
          at === 'ORDER_DETAIL' &&
          order.cancel_enabled &&
          order._isInWhileList &&
          !order._isCashierMode &&
          order.order_flags !== GROUP_BUY_ORDER
        )
      },
      do(params, cb = _.noop) {
        cb()
      }
    },
    {
      label: '申请退款',
      weight: 0,
      value: 'refund',
      visible: false,
      sort: 0,
      iconClassName: 'apply-refund',
      icon: 'iconRefundUrl',
      when(item) {
        return (
          at === 'ORDER_DETAIL' &&
          order.refund_apply_enabled &&
          order._isInWhileList &&
          (!order._isCashierMode || isInCampus) &&
          order.order_flags !== GROUP_BUY_ORDER
        )
      },
      do(params, cb = _.noop) {
        cb()
      }
    },
    {
      label: '撤销申请',
      weight: 0,
      value: 'cancelRefund',
      visible: false,
      sort: 0,
      iconClassName: 'cancel-refund',
      icon: 'iconCancelRefundUrl',
      highlight: true,
      when(item) {
        return (
          at === 'ORDER_DETAIL' &&
          order.refund_revoke_enabled &&
          order._isInWhileList &&
          !order._isCashierMode
        )
      },
      do(params, cb = _.noop) {
        cb()
      }
    },
    {
      label: '再来一单',
      weight: 0,
      value: 'reOrder',
      visible: false,
      sort: 0,
      iconClassName: 're-order',
      icon: 'btnReorderIconUrl',
      when(item) {
        // 显示再来一单
        // 605 订单异常
        // 606 已取消配送单
        const shouldShowReOrder =
          _.includes([...FINAL_STATE, ...WAIT_PAYED_STATUS, 605, 606], status) &&
          type !== EAT_FIRST_ORDER &&
          type !== PAY_FIRST_TABLE_ORDER &&
          !isMealInStore &&
          !isRoundMeal &&
          !isLoveFeastOrder(order)
        // 设置高亮
        if (shouldShowReOrder) {
          _.assign(item, {
            visible: true,
            highlight: isHighlightInReOrder
          })
        }
        return shouldShowReOrder
      },
      do(params, cb = _.noop) {
        const { trackName, ...order } = params
        const btnName = trackName === 'SmMpUIViewClick' ? 'my_order_btn' : 'order_details_btn'
        this.call('track', trackName, {
          [btnName]: 'one_more_order',
          sm_uiview_name: `${pagesMap[at]}-再来一单`
        })
        reOrder.bind(this)(order)
        cb()
      }
    },
    {
      label: '致电商家',
      weight: 1,
      value: 'callMerchant',
      visible: false,
      sort: 1,
      iconClassName: 'call-merchant',
      icon: 'btnCallMerchantIconUrl',
      do(params, cb = _.noop) {
        const { btnItem: item, position, trackName, ...order } = params
        const btnName = trackName === 'SmMpUIViewClick' ? 'my_order_btn' : 'order_details_btn'
        this.call('track', trackName, {
          [btnName]: 'call_merchant',
          sm_uiview_name: `${pagesMap[at]}-致电商家`
        })
        item.phone && callPhone(item.phone)
        cb()
      },
      when(item) {
        // 显示致电商家
        const shouldShowCallMerchant = !(
          (type === SUBSCRIBE_ORDER &&
            _.includes(_.concat(WAIT_PAYED_STATUS, MERCHANT_RECEIVED_ORDER), status)) ||
          (type === EAT_FIRST_ORDER && _.includes(['INIT', 'ACCEPTED'], process_status)) ||
          (type === PAY_FIRST_TABLE_ORDER &&
            _.includes([INIT, ACCEPTED, NOT_ACCEPTED], process_status))
        )

        if (shouldShowCallMerchant) {
          _.assign(item, {
            visible: true,
            phone: merchantPhone,
            highlight: isHighlightCallMerchant
          })
        }

        return shouldShowCallMerchant
      }
    },
    {
      do(params, cb = _.noop) {
        const { btnItem: item, trackName } = params
        const btnName = trackName === 'SmMpUIViewClick' ? 'my_order_btn' : 'order_details_btn'
        this.call('track', trackName, {
          [btnName]: 'call_rider',
          sm_uiview_name: `${pagesMap[at]}-致电骑手`
        })
        callPhone(item.phone)
        cb()
      },
      label: '致电骑手',
      weight: 1,
      value: 'callMan',
      visible: false,
      sort: 2,
      iconClassName: 'call-rider',
      icon: 'btnCallManIconUrl',
      when(item) {
        const dm_mobile = _.get(order, 'extra.dm_info.dm_mobile')
        if (dm_mobile) {
          _.assign(item, {
            visible: true,
            phone: dm_mobile,
            highlight: isHighlightCallMan
          })
        }
        return dm_mobile
      }
    },
    {
      do(params, cb = _.noop) {
        const { trackName, ...order } = params
        const btnName = trackName === 'SmMpUIViewClick' ? 'my_order_btn' : 'order_details_btn'
        this.call('track', trackName, {
          [btnName]: 'order_details',
          sm_uiview_name: `${pagesMap[at]}-去支付`
        })
        rePay.bind(this)(order)
        cb()
      },
      label: '去支付',
      weight: 1,
      value: 'pay',
      visible: true,
      sort: 3,
      iconClassName: 'pay-order',
      icon: 'btnPayIconUrl',
      when(item) {
        const shouldShowPay =
          (_.includes(WAIT_PAYED_STATUS, status) && type !== PAY_FIRST_TABLE_ORDER) ||
          (type === EAT_FIRST_ORDER &&
            _.includes(['INIT', 'ACCEPTED'], process_status) &&
            status !== PAID_AND_ACCEPTED_STATUS) ||
          (type === PAY_FIRST_TABLE_ORDER && pay_controll && pay_controll.allow_pay)

        if (shouldShowPay) {
          _.assign(item, { visible: true, highlight: true })
        }

        return shouldShowPay
      }
    },
    {
      async do(payload = {}) {
        const { isInCampus } = this.data

        this.campusvc = new CampusService()
        // const url = await this.campusvc.getServiceLink(params);
        const url = await getCustomerServiceUrl.call(this, payload, this.store, isInCampus)
        // ? 收钱吧是否有问题？
        if (url) {
          return this.data.sqbBridge.navigateTo({
            path: '/pages/web/index',
            query: {
              url
            }
          })
        }
      },
      label: '联系客服',
      weight: 1,
      value: 'service',
      visible: true,
      sort: 4,
      iconClassName: 'contact-service',
      icon: 'btnServiceIconUrl',
      when(item) {
        // 外卖自取订单
        // 门店入驻校园
        // 有支付时间
        // 支付时间在2天内
        // 显示"联系客服"
        let isInTimeLimit = false
        if (pay_time) {
          isInTimeLimit =
            // @ts-ignore
            dayjs().valueOf() <
            // @ts-ignore
            dayjs(pay_time).add(2, 'day').valueOf()
        }
        return (
          (type === TAKE_OUT_ORDER || type === PRE_ORDER) && isInCampus && pay_time && isInTimeLimit
        )
      }
    }
  ]

  return _.filter(actions, item => item.when(item))
}
const filterForOrderDetail = (order, originalStatus, btnList) => {
  const type = _.get(order, 'type')
  if (type === SUBSCRIBE_ORDER && _.includes(MERCHANT_RECEIVED_ORDER, originalStatus)) {
    btnList = _.filter(
      btnList,
      ({ value }) =>
        value !== 'reOrder' &&
        value !== 'cancelOrder' &&
        value !== 'refund' &&
        value !== 'cancelRefund'
    )
  }
  return btnList
}
const filterForOrderList = (originalStatus, btnList) => {
  /**
   * 订单完成
   * 不显示联系商家
   */
  // @ts-ignore
  if (_.includes(MEAL_ORDER_CANCELED_STATUS.concat(ORDER_FINISH_STATUS), originalStatus)) {
    btnList = _.filter(btnList, ({ value }) => value !== 'callMerchant')
  }
  /**
   *  骑手正常工作
   *  不显示联系骑手
   */
  if (!_.includes(RIDER_WORKING, originalStatus)) {
    btnList = _.filter(btnList, ({ value }) => value !== 'callMan')
  }
  return btnList
}

/**
 * 重新支付
 * @param order
 * @returns {Promise<void>}
 */
export async function rePay(order) {
  const { id, sn, effective_amount, table_id, type } = order || {}

  if (type === PAY_FIRST_TABLE_ORDER) {
    this.redirect('page-firstpay-round-batch-list', { client_sn: sn, tableId: table_id })
    return
  }

  if (type === EAT_FIRST_ORDER) {
    this.redirect('page-submit-order', { client_sn: sn, tableId: table_id })
    return
  }
  this.redirect('page-order-pay', { id, sn, paidAmount: effective_amount, table_id })
}

export async function reOrder(order) {
  const { sn, store_id: storeId, type } = order || {}
  if (!sn) return
  const opts = getQueryOpts.call(this)

  const cartService = new CartService(opts)
  try {
    const result = await cartService.reOrder({ sn, storeId })
    const { success, price, invalid } = result || {}
    if (!success) {
      // @ts-ignore
      storage({
        [handleHashKey('goodsChange', storeId)]: { invalid, price }
      })
    }
    this.redirect('page-home', {
      storeId,
      buyAgainOrderType: type
    })
  } catch (error) {
    console.error('reOrder', error)
    // TODO: 这里应该看到很快到toast
    await cartService.clear({ storeId }).catch(err => console.log('cartService clear', err))
    const duration = 2000
    wx.showToast({
      title: error.message || '商品信息已发生变更，请重新选购',
      duration,
      icon: 'none',
      complete: () => {
        setTimeout(() => this.redirect('page-home', { storeId, buyAgainOrderType: type }), duration)
      }
    })
    // this.$toast({
    //   message: error.message || '商品信息已发生变更，请重新选购', onClose: () => {
    //     this.redirect('page-home', { storeId, buyAgainOrderType: type })
    //   }
    // })
    return
  }

  // wx.reLaunch({
  //   url: `/E1QDEFPQUJWB/s11gfxefx0j6/index?buyAgainOrderType=${type}&storeId=${storeId}`,
  //   success(res) {
  //     console.log('跳转成功', res)
  //   },
  //   fail(err) {
  //     console.error('跳转失败', err)
  //     // 降级处理
  //     wx.redirectTo({
  //       url: `/E1QDEFPQUJWB/s11gfxefx0j6/index?buyAgainOrderType=${type}&storeId=${storeId}`
  //     })
  //   }
  // })
}

/**
 * 根据订单状态，构建订单按钮数据
 * @param order
 * @param {string: 'ORDER_DETAIL'|'ORDER_LIST'} at
 * @returns {*}
 */
export const getActionsByOrder = (order, at = 'ORDER_DETAIL') => {
  //  btnList  0:再来一单 reOrder  1:致电商家  callMerchant 2:致电骑手 callMan 3:去支付 pay
  if (!order) return
  let btnList = buildActionsByOrder(order, at)
  // 如果是点单
  // 订单状态为: 600, 601, 609, 200, 611, 610
  // 只显示"再来一单"
  const originalStatus = _.get(order, 'status')
  // 只在订单详情显示
  if (at === 'ORDER_DETAIL') {
    btnList = filterForOrderDetail(order, originalStatus, btnList)
  }
  // 只在订单列表显示
  if (at === 'ORDER_LIST') {
    btnList = filterForOrderList(originalStatus, btnList)
  }

  return btnList
}

export function isConvertToFinish(order) {
  // const SIX_HOUR = 60 * 1000 * 60 * 6;
  const ONE_HOUR = 60 * 1000 * 60
  const { mtime, status, type, process_status } = order || {}
  //  围餐兼容轻餐
  const roundMealFinish =
    (type === EAT_FIRST_ORDER || type === PAY_FIRST_TABLE_ORDER) &&
    _.includes(ROUND_MEAL_FINISH, process_status)
  // 是围餐
  // 订单process_status 为['ACCEPT_FAILED', 'COMPLETED', 'CLOSED', 'REVOKED'];
  // 或者 订单status非[101, 102, 103, 300, 302, 301, 303]且订单修改时间超过6个小时
  // 或者 订单修改超过1个小时&&为店内点单且&&订单status为200
  // 返回true
  return (
    roundMealFinish ||
    // (!_.includes(UN_NORMAL_ORDER, status) && Date.now() - mtime >= SIX_HOUR) ||
    (Date.now() - mtime >= ONE_HOUR && type === SUBSCRIBE_ORDER && status === 200)
  )
}
