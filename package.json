{"private": false, "name": "@wosai/smart-mp-biz", "version": "1.8.0", "description": "智慧经营小程序业务库", "files": ["dist/biz/web", "dist/biz/wechat", "dist/biz/alipay"], "miniprogram": "dist/biz/wechat", "maintainers": ["<PERSON><PERSON><PERSON><PERSON>"], "scripts": {"dev:wx": "mor compile --name wechat -w", "build:wx": "mor compile --name wechat --production", "dev:wx:app": "npm run dev:wx --prefix ./smart-mp-dev", "dev:wx:home": "package_name=home npx mor compile --name wechat -w", "dev:wx:order": "package_name=order mor compile --name wechat -w", "dev:wx:store": "package_name=store mor compile --name wechat -w", "dev:wx:purchased": "package_name=purchased mor compile --name wechat -w", "dev:wx:center": "package_name=center mor compile --name wechat -w", "dev:wx:home:prod": "package_name=home npx mor compile --name wechat --production -w", "dev:wx:order:prod": "package_name=order mor compile --name wechat --production -w", "dev:wx:store:prod": "package_name=store mor compile --name wechat --production -w", "dev:wx:purchased:prod": "package_name=purchased mor compile --name wechat --production -w", "dev:wx:center:prod": "package_name=center mor compile --name wechat --production -w", "dev:wx:love": "package_name=love mor compile --name wechat -w", "dev:wx:roundmeal": "package_name=roundmeal mor compile --name wechat -w", "dev:alipay:home:prod": "package_name=home npx mor compile --name alipay --production -w", "dev:alipay:order:prod": "package_name=order mor compile --name alipay --production -w", "dev:alipay:center:prod": "package_name=center mor compile --name alipay --production -w", "dev:alipay:store:prod": "package_name=store mor compile --name alipay --production -w", "dev:alipay:purchased:prod": "package_name=purchased mor compile --name alipay --production -w", "dev:alipay:roundmeal:prod": "package_name=roundmeal mor compile --name alipay --production -w", "dev:alipay:home": "package_name=home npx mor compile --name alipay -w", "dev:alipay:order": "package_name=order mor compile --name alipay -w", "dev:alipay:center": "package_name=center mor compile --name alipay -w", "dev:alipay:store": "package_name=store mor compile --name alipay -w", "dev:alipay:purchased": "package_name=purchased mor compile --name alipay -w", "dev:alipay:roundmeal": "package_name=roundmeal mor compile --name alipay -w", "dev:ali": "mor compile --name alipay -w", "dev:ali:app": "npm run dev:ali --prefix ./smart-mp-dev", "build:all": "npm run build:wx:all && npm run build:ali:all", "build:wx:all": "npm run build:wx:home && npm run build:wx:order && npm run build:wx:center && npm run build:wx:store && npm run build:wx:love && npm run build:wx:purchased && npm run build:wx:roundmeal", "build:ali:all": "npm run build:alipay:home && npm run build:alipay:order && npm run build:alipay:center && npm run build:alipay:store && npm run build:alipay:love && npm run build:alipay:purchased && npm run build:alipay:roundmeal", "build:wx:home": "package_name=home mor compile --name wechat --production", "build:wx:order": "package_name=order mor compile --name wechat --production", "build:wx:store": "package_name=store mor compile --name wechat --production", "build:wx:purchased": "package_name=purchased mor compile --name wechat --production", "build:wx:center": "package_name=center mor compile --name wechat --production", "build:wx:love": "package_name=love mor compile --name wechat --production", "build:wx:roundmeal": "package_name=roundmeal mor compile --name wechat --production", "build:alipay:home": "package_name=home mor compile --name alipay --production", "build:alipay:order": "package_name=order mor compile --name alipay --production", "build:alipay:store": "package_name=store mor compile --name alipay --production", "build:alipay:purchased": "package_name=purchased mor compile --name alipay --production", "build:alipay:center": "package_name=center mor compile --name alipay --production", "build:alipay:love": "package_name=love mor compile --name alipay --production", "build:alipay:roundmeal": "package_name=roundmeal mor compile --name alipay --production", "build:web": "mor compile --name web --production", "biz:link": "rm -rf ./smart-mp-dev/node_modules/@wosai/smart-mp-biz && sh -c 'ln -s \"$(pwd)\" ./smart-mp-dev/node_modules/@wosai/smart-mp-biz'", "test": "jest .", "format": "prettier --write 'src/**/*.{js,ts,json,css,less,wxml}'", "lint": "tslint --project .", "prepare": "husky install", "generate:type": "ts-node ./scripts/generate_type.ts", "update-submodules": "git submodule update --remote --merge", "create:lodash": "node ./scripts/create-minist-lodash/index.js"}, "keywords": [], "author": "<PERSON>", "license": "ISC", "dependencies": {"@sqb/ads-preload": "*", "@sqb/mp-behavior-openqrpay": "1.3.0", "@sqb/mp-comment-entry": "1.0.0", "@sqb/mp-pay-plugin-direct": "3.11.0", "@sqb/sdk-pay-sodexo-card": "*", "@wosai/emenu-mini-config": "7.81.1", "@wosai/emenu-mini-dayjs": "7.81.1", "@wosai/emenu-mini-md5": "7.81.1", "@wosai/emenu-mini-models": "7.81.1", "@wosai/emenu-mini-request": "7.81.1", "@wosai/emenu-mini-services": "7.81.1", "@wosai/emenu-mini-store": "7.81.1", "@wosai/emenu-mini-utils": "7.81.1", "@wosai/smart-mp-cli": "*", "@wosai/smart-mp-lodash": "1.1.1", "qs": "6.8.0", "zod": "^3.23.8"}, "devDependencies": {"@babel/cli": "^7.24.7", "@babel/core": "^7.26.9", "@babel/plugin-transform-modules-commonjs": "^7.26.3", "@babel/preset-env": "^7.26.9", "@babel/preset-typescript": "^7.26.0", "@babel/register": "^7.24.6", "@changesets/cli": "^2.27.5", "@eslint/js": "^9.15.0", "@happy-dom/global-registrator": "^14.12.0", "@morjs/cli": "1.0.113", "@morjs/core": "1.0.86", "@testing-library/dom": "^10.4.0", "@total-typescript/ts-reset": "^0.4.2", "@types/jest": "^27.5.2", "@types/lodash": "^4.17.13", "@types/node": "^14.14.31", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "babel-jest": "^29.7.0", "chalk": "^4.1.2", "concurrently": "^8.2.2", "eslint": "^9.15.0", "eslint-define-config": "^1.20.0", "flush-promises": "^1.0.2", "fs-extra": "^9.1.0", "globals": "^15.12.0", "husky": "^8.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "lerna": "4.0.0", "less": "^4.2.0", "less-loader": "^12.2.0", "lint-staged": "^15.0.0", "miniprogram-api-typings": "^3.12.3", "miniprogram-simulate": "^1.6.1", "ora": "^5.4.1", "postcss": "^8.4.38", "postcss-less": "^6.0.0", "postcss-loader": "^8.1.1", "postcss-px-to-viewport": "^1.1.1", "prettier": "^2.2.1", "prettier-plugin-wxml": "^3.0.0", "purgecss": "^7.0.2", "purgecss-webpack-plugin": "^7.0.2", "request": "^2.88.2", "terser": "^5.39.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "4.4.4", "typescript-eslint": "^8.15.0", "zod-to-ts": "^1.2.0"}, "resolutions": {"@wosai/emenu-mini-lodash": "7.80.1", "@wosai/emenu-mini-utils": "7.80.1", "@wosai/emenu-mini-dayjs": "7.80.1", "@wosai/emenu-mini-services": "7.80.1", "@wosai/emenu-mini-emenu": "7.80.1", "@wosai/emenu-mini-emenuX": "7.80.1", "jackspeak": "3.0.0", "path-scurry": "1.9.2", "lru-cache": "8.0.5", "semver": "7.3.8", "typescript": "4.4.4"}, "peerDependencies": {"@morjs/core": "1.0.86"}, "lint-staged": {"*.{js,ts,json,css,less,wxml}": ["npm run format"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "overrides": {"typescript": "4.4.4", "@wosai/smart-mp-concise-cli": {"engines": {"node": ">=18.18.2"}}, "@wosai/smart-mp-concise-compiler": {"engines": {"node": ">=18.18.2"}}, "jackspeak": "3.0.0", "path-scurry": "1.9.2", "lru-cache": "8.0.5", "semver": "7.3.8", "@babel/helper-compilation-targets": {"lru-cache": "8.0.5"}}}