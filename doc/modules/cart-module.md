# 购物车模块详细文档

## 模块概述

购物车模块是 Smart MP Biz 的核心业务模块，负责商品的添加、删除、数量修改、优惠计算、本地存储和服务端同步。采用单例模式设计，支持多种商品类型和复杂的优惠计算。

## 技术架构

### 核心类结构

```
src/stores/
├── CartStore.ts                # 购物车状态管理
├── CartModel.ts                # 购物车数据模型
├── helper.ts                   # 购物车工具函数
├── api.ts                      # 购物车API接口
└── constants.ts                # 购物车常量定义
```

### 设计模式

- **单例模式**：全局唯一的购物车实例
- **观察者模式**：购物车状态变更通知
- **钩子模式**：支持插件化扩展
- **策略模式**：不同商品类型的处理策略

## 核心功能实现

### 1. 购物车初始化

```typescript
class CartStore extends Store {
  constructor(view, data) {
    super(view, data);
    this.cartModel = new CartModel();
    this.initHooks();
    this.loadLocalCart();
  }
  
  static getInstance(view, data): CartStore {
    if (!this.instance) {
      this.instance = new CartStore(view, data);
    }
    return this.instance;
  }
}
```

### 2. 商品加购流程

```typescript
async addToCart(goods: IAddCartGoods) {
  // 1. 前置钩子处理
  const processedGoods = await this.hooks.addBefore.promise(goods);
  
  // 2. 本地购物车更新
  const cart = this.updateLocalCart(processedGoods);
  
  // 3. 计算优惠
  const cartWithDiscount = await this.calculateDiscount(cart);
  
  // 4. 更新UI
  this.updateUI(cartWithDiscount);
  
  // 5. 同步到服务端
  await this.syncToServer(cartWithDiscount);
  
  // 6. 后置钩子处理
  await this.hooks.add.promise(cartWithDiscount);
}
```

### 3. 优惠计算机制

```typescript
async calculateDiscount(cart: ICart): Promise<ICart> {
  // 1. 商品级优惠
  const goodsDiscount = this.calculateGoodsDiscount(cart);
  
  // 2. 订单级优惠
  const orderDiscount = this.calculateOrderDiscount(cart);
  
  // 3. 会员优惠
  const memberDiscount = this.calculateMemberDiscount(cart);
  
  // 4. 活动优惠
  const activityDiscount = this.calculateActivityDiscount(cart);
  
  // 5. 合并优惠
  return this.mergeDiscounts(cart, [
    goodsDiscount,
    orderDiscount, 
    memberDiscount,
    activityDiscount
  ]);
}
```

## 数据结构设计

### 购物车数据结构

```typescript
interface ICart {
  // 基础信息
  client_version: number;
  total: number;              // 商品总数量
  total_price: number;        // 商品总价格
  
  // 商品记录
  records: ICartRecord[];
  
  // 优惠信息
  discount: IDiscount;
  
  // 扩展信息
  extra?: ICartExtra;
}

interface ICartRecord {
  goodsId: string;           // 商品ID
  num: number;               // 数量
  price: number;             // 单价
  totalPrice: number;        // 总价
  attachInfo?: IAttachInfo;  // 附加信息（规格等）
  discount?: IRecordDiscount; // 商品级优惠
}

interface IDiscount {
  total_discount: number;    // 总优惠金额
  activities: IActivity[];   // 优惠活动列表
  coupons: ICoupon[];       // 优惠券列表
}
```

### 商品附加信息

```typescript
interface IAttachInfo {
  // 规格信息
  specs?: ISpec[];
  
  // 套餐信息
  combo?: IComboInfo;
  
  // 围餐信息
  roundMeal?: IRoundMealInfo;
  
  // 自定义信息
  custom?: Record<string, any>;
}

interface ISpec {
  specId: string;
  specName: string;
  optionId: string;
  optionName: string;
  price: number;
}
```

## 核心方法详解

### addToCart - 添加商品

```typescript
async addToCart(goods: IAddCartGoods): Promise<IAddCartResult> {
  try {
    // 1. 参数验证
    this.validateGoods(goods);
    
    // 2. 库存检查
    await this.checkStock(goods);
    
    // 3. 业务规则检查
    await this.checkBusinessRules(goods);
    
    // 4. 执行加购
    const result = await this.executeAddToCart(goods);
    
    // 5. 触发事件
    this.emit(CART_CHANGE_EVENT, result);
    
    return result;
  } catch (error) {
    this.handleError(error);
    throw error;
  }
}
```

### removeFromCart - 移除商品

```typescript
async removeFromCart(goodsId: string, num?: number): Promise<void> {
  const cart = this.getCart();
  const record = cart.records.find(r => r.goodsId === goodsId);
  
  if (!record) return;
  
  if (num && num < record.num) {
    // 减少数量
    record.num -= num;
    record.totalPrice = record.price * record.num;
  } else {
    // 完全移除
    const index = cart.records.indexOf(record);
    cart.records.splice(index, 1);
  }
  
  // 重新计算总价
  this.recalculateCart(cart);
  
  // 同步到服务端
  await this.syncToServer(cart);
}
```

### syncToServer - 同步到服务端

```typescript
async syncToServer(cart: ICart): Promise<void> {
  try {
    // 1. 前置钩子
    const processedCart = await this.hooks.syncBefore.promise(cart);
    
    // 2. 发送请求
    const response = await this.cartModel.syncCart(processedCart);
    
    // 3. 处理响应
    const serverCart = this.processServerResponse(response);
    
    // 4. 更新本地数据
    this.updateLocalCart(serverCart);
    
    // 5. 后置钩子
    await this.hooks.syncSuccess.promise(serverCart);
    
  } catch (error) {
    // 同步失败处理
    this.hooks.syncFail.call(error);
    throw error;
  }
}
```

## 钩子系统

### 钩子类型

```typescript
interface CartHooks {
  // 初始化钩子
  init: AsyncSeriesWaterfallHook;
  
  // 加购相关钩子
  addBefore: AsyncSeriesWaterfallHook;
  add: AsyncSeriesWaterfallHook;
  addSuccess: SyncHook;
  
  // 同步相关钩子
  syncBefore: AsyncSeriesWaterfallHook;
  syncSuccess: AsyncSeriesWaterfallHook;
  syncFail: SyncHook;
  
  // 其他钩子
  clear: SyncHook;
  reset: SyncHook;
}
```

### 钩子使用示例

```typescript
// 注册钩子
cartStore.hooks.addBefore.tapAsync('validateGoods', async (goods) => {
  if (!goods.goodsId) {
    throw new Error('商品ID不能为空');
  }
  return goods;
});

// 注册同步钩子
cartStore.hooks.syncSuccess.tap('updateBadge', (cart) => {
  BadgeStore.getInstance().updateCartBadge(cart.total);
});
```

## 缓存策略

### 本地存储

```typescript
class CartStore {
  // 保存到本地存储
  saveToLocal(cart: ICart): void {
    const key = this.getStorageKey();
    StorageUtils.storage(key, cart);
  }
  
  // 从本地存储加载
  loadFromLocal(): ICart | null {
    const key = this.getStorageKey();
    return StorageUtils.storage(key) || null;
  }
  
  // 获取存储键
  private getStorageKey(): string {
    const storeId = this.getStoreId();
    const mealType = this.getMealType();
    return `cart:${storeId}:${mealType}`;
  }
}
```

### 缓存失效策略

- **时间失效**：购物车数据缓存2小时
- **版本失效**：服务端版本号变更时失效
- **业务失效**：切换店铺或服务类型时失效

## 并发控制

### 防抖处理

```typescript
class CartStore {
  private addToCartDebounce = _.debounce(
    this.syncToServer.bind(this), 
    500
  );
  
  async addToCart(goods: IAddCartGoods) {
    // 立即更新本地UI
    this.updateLocalCart(goods);
    
    // 防抖同步到服务端
    this.addToCartDebounce();
  }
}
```

### 请求队列

```typescript
class CartModel {
  private requestQueue: Promise<any>[] = [];
  
  async syncCart(cart: ICart): Promise<any> {
    const request = this.doSyncCart(cart);
    this.requestQueue.push(request);
    
    try {
      return await request;
    } finally {
      const index = this.requestQueue.indexOf(request);
      this.requestQueue.splice(index, 1);
    }
  }
}
```

## 错误处理

### 错误类型

```typescript
enum CartErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  BUSINESS_ERROR = 'BUSINESS_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  STOCK_ERROR = 'STOCK_ERROR'
}

class CartError extends Error {
  constructor(
    public type: CartErrorType,
    public message: string,
    public data?: any
  ) {
    super(message);
  }
}
```

### 错误处理策略

```typescript
class CartStore {
  handleError(error: CartError): void {
    switch (error.type) {
      case CartErrorType.NETWORK_ERROR:
        // 网络错误：显示重试提示
        this.showRetryDialog();
        break;
        
      case CartErrorType.STOCK_ERROR:
        // 库存错误：更新商品状态
        this.updateGoodsStock(error.data);
        break;
        
      case CartErrorType.BUSINESS_ERROR:
        // 业务错误：显示错误提示
        showToast(error.message);
        break;
        
      default:
        // 未知错误：记录日志
        console.error('未知购物车错误', error);
    }
  }
}
```

## 性能优化

### 1. 批量操作

```typescript
// 批量添加商品
async batchAddToCart(goodsList: IAddCartGoods[]): Promise<void> {
  // 本地批量更新
  const cart = this.batchUpdateLocalCart(goodsList);
  
  // 一次性同步到服务端
  await this.syncToServer(cart);
}
```

### 2. 内存优化

```typescript
// 清理过期数据
cleanupExpiredData(): void {
  const now = Date.now();
  const expireTime = 2 * 60 * 60 * 1000; // 2小时
  
  this.cartDataStore.forEach((value, key) => {
    if (now - value.timestamp > expireTime) {
      this.cartDataStore.delete(key);
    }
  });
}
```

### 3. 计算优化

```typescript
// 优惠计算缓存
private discountCache = new Map<string, IDiscount>();

calculateDiscount(cart: ICart): IDiscount {
  const cacheKey = this.getDiscountCacheKey(cart);
  
  if (this.discountCache.has(cacheKey)) {
    return this.discountCache.get(cacheKey);
  }
  
  const discount = this.doCalculateDiscount(cart);
  this.discountCache.set(cacheKey, discount);
  
  return discount;
}
```

## 测试策略

### 单元测试

```typescript
describe('CartStore', () => {
  let cartStore: CartStore;
  
  beforeEach(() => {
    cartStore = CartStore.getInstance();
    cartStore.reset();
  });
  
  test('添加商品到购物车', async () => {
    const goods = { goodsId: '123', num: 1 };
    await cartStore.addToCart(goods);
    
    const cart = cartStore.getCart();
    expect(cart.records).toHaveLength(1);
    expect(cart.records[0].goodsId).toBe('123');
  });
  
  test('库存不足时抛出错误', async () => {
    const goods = { goodsId: '456', num: 999 };
    
    await expect(cartStore.addToCart(goods))
      .rejects
      .toThrow('库存不足');
  });
});
```

### 集成测试

- **数据同步测试**：验证本地与服务端数据一致性
- **并发操作测试**：验证并发加购的正确性
- **错误恢复测试**：验证网络异常后的恢复能力

## 监控指标

### 性能指标

- **加购响应时间**：本地加购响应时间 < 100ms
- **同步成功率**：购物车同步成功率 > 99%
- **缓存命中率**：本地缓存命中率 > 80%

### 业务指标

- **加购成功率**：用户加购操作成功率
- **购物车转化率**：从加购到下单的转化率
- **平均购物车价值**：用户平均购物车金额