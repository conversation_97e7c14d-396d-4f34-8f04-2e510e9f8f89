# 公共组件模块详细文档

## 模块概述

公共组件模块提供了一套完整的可复用UI组件和业务组件，支持主题定制、多平台适配和组件化开发。组件分为基础UI组件和业务组件两大类。

## 组件架构

### 组件分层结构

```
src/
├── lib/ui/                     # 基础UI组件层
│   ├── button/                # 按钮组件
│   ├── dialog/                # 弹窗组件
│   ├── popup/                 # 弹出层组件
│   └── ...                    # 其他基础组件
├── components_v2/             # 业务组件层
│   ├── goods/                 # 商品相关组件
│   ├── counter/               # 计数器组件
│   ├── dialog-*/              # 业务弹窗组件
│   └── ...                    # 其他业务组件
└── behaviors/                 # 组件行为层
    ├── theme.ts               # 主题行为
    ├── i18n.ts                # 国际化行为
    └── ...                    # 其他行为
```

### 组件设计原则

1. **单一职责**：每个组件只负责一个特定功能
2. **可复用性**：组件可在不同场景下复用
3. **可配置性**：通过属性配置组件行为和样式
4. **可扩展性**：支持插槽和事件扩展
5. **一致性**：遵循统一的设计规范

## 基础UI组件

### Button 按钮组件

```typescript
// src/lib/ui/button/index.ts
Component({
  properties: {
    type: {
      type: String,
      value: 'default' // default, primary, danger, warning
    },
    size: {
      type: String,
      value: 'normal' // mini, small, normal, large
    },
    disabled: {
      type: Boolean,
      value: false
    },
    loading: {
      type: Boolean,
      value: false
    },
    block: {
      type: Boolean,
      value: false
    }
  },
  
  methods: {
    onClick(e) {
      if (this.data.disabled || this.data.loading) return;
      this.triggerEvent('click', e.detail);
    }
  }
});
```

### Dialog 弹窗组件

```typescript
// src/lib/ui/dialog/index.ts
Component({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    title: {
      type: String,
      value: ''
    },
    content: {
      type: String,
      value: ''
    },
    showCancel: {
      type: Boolean,
      value: true
    },
    cancelText: {
      type: String,
      value: '取消'
    },
    confirmText: {
      type: String,
      value: '确定'
    }
  },
  
  methods: {
    onCancel() {
      this.triggerEvent('cancel');
    },
    
    onConfirm() {
      this.triggerEvent('confirm');
    },
    
    onMaskClick() {
      if (this.data.maskClosable) {
        this.onCancel();
      }
    }
  }
});
```

## 业务组件

### Goods 商品组件

商品组件是最复杂的业务组件之一，支持多种展示模式和交互方式。

```typescript
// src/components_v2/goods/basic/index.ts
BaseComponent({
  properties: {
    info: {
      type: Object,
      value: {}
    },
    counterType: {
      type: String,
      value: 'counter'
    },
    showPrice: {
      type: Boolean,
      value: true
    },
    showGoodsNum: {
      type: Boolean,
      value: true
    },
    useCounter: {
      type: Boolean,
      value: false
    }
  },
  
  computed: {
    // 计算商品显示价格
    displayPrice() {
      const { info } = this.data;
      return info.discountPrice || info.price;
    },
    
    // 计算商品状态
    goodsStatus() {
      const { info } = this.data;
      if (info.stock <= 0) return 'sold-out';
      if (info.isRecommend) return 'recommend';
      return 'normal';
    }
  },
  
  methods: {
    // 商品点击事件
    onGoodsClick() {
      this.triggerEvent('goodsClick', {
        goods: this.data.info
      });
    },
    
    // 加购事件
    onAddToCart(e) {
      const { num = 1 } = e.detail;
      this.triggerEvent('addToCart', {
        goods: this.data.info,
        num
      });
    }
  }
});
```

### Counter 计数器组件

```typescript
// src/components_v2/counter/index.ts
BaseComponent({
  properties: {
    value: {
      type: Number,
      value: 0
    },
    min: {
      type: Number,
      value: 0
    },
    max: {
      type: Number,
      value: 999
    },
    step: {
      type: Number,
      value: 1
    },
    disabled: {
      type: Boolean,
      value: false
    }
  },
  
  computed: {
    minusDisabled() {
      return this.data.disabled || this.data.value <= this.data.min;
    },
    
    plusDisabled() {
      return this.data.disabled || this.data.value >= this.data.max;
    }
  },
  
  methods: {
    onMinus() {
      if (this.minusDisabled) return;
      
      const newValue = Math.max(
        this.data.min, 
        this.data.value - this.data.step
      );
      
      this.triggerEvent('change', { value: newValue });
    },
    
    onPlus() {
      if (this.plusDisabled) return;
      
      const newValue = Math.min(
        this.data.max, 
        this.data.value + this.data.step
      );
      
      this.triggerEvent('change', { value: newValue });
    }
  }
});
```

## 组件行为系统

### Theme 主题行为

```typescript
// src/behaviors/theme.ts
export const useThemeBehavior = Behavior({
  data: {
    themeConfig: {}
  },
  
  attached() {
    this.updateTheme();
  },
  
  methods: {
    updateTheme() {
      const themeConfig = getGlobalTheme();
      this.setData({ themeConfig });
    },
    
    getThemeVar(key: string): string {
      return this.data.themeConfig[key] || '';
    }
  }
});
```

### I18n 国际化行为

```typescript
// src/behaviors/i18n.ts
export const useI18nBehavior = Behavior({
  methods: {
    t(key: string, params?: Record<string, any>): string {
      return i18n.t(key, params);
    },
    
    setLocale(locale: string) {
      i18n.setLocale(locale);
      this.updateI18nData();
    },
    
    updateI18nData() {
      // 更新组件中的国际化文本
      this.setData({
        i18nData: this.getI18nData()
      });
    }
  }
});
```

## 组件通信机制

### 事件通信

```typescript
// 父组件
Component({
  methods: {
    onGoodsAddToCart(e) {
      const { goods, num } = e.detail;
      console.log('商品加购', goods, num);
    }
  }
});

// 子组件
Component({
  methods: {
    addToCart() {
      this.triggerEvent('addToCart', {
        goods: this.data.goods,
        num: 1
      });
    }
  }
});
```

### 全局事件总线

```typescript
// src/utils/events/index.js
class EventBus {
  constructor() {
    this.events = {};
  }
  
  on(event, callback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }
  
  emit(event, data) {
    if (this.events[event]) {
      this.events[event].forEach(callback => callback(data));
    }
  }
  
  off(event, callback) {
    if (this.events[event]) {
      const index = this.events[event].indexOf(callback);
      if (index > -1) {
        this.events[event].splice(index, 1);
      }
    }
  }
}

export const eventBus = new EventBus();
```

## 主题系统

### 主题配置

```typescript
// 主题配置接口
interface IThemeConfig {
  // 颜色配置
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  textColor: string;
  
  // 字体配置
  fontSize: {
    small: string;
    normal: string;
    large: string;
  };
  
  // 间距配置
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  
  // 圆角配置
  borderRadius: {
    small: string;
    normal: string;
    large: string;
  };
}
```

### 主题应用

```less
// 组件样式中使用主题变量
.goods-item {
  background-color: var(--background-color);
  border-radius: var(--border-radius-normal);
  padding: var(--spacing-md);
  
  .goods-name {
    color: var(--text-color);
    font-size: var(--font-size-normal);
  }
  
  .goods-price {
    color: var(--primary-color);
    font-size: var(--font-size-large);
  }
}
```

## 组件测试

### 单元测试

```typescript
// src/components_v2/counter/__tests__/counter.test.ts
import { render, fireEvent } from '@testing-library/miniprogram';
import Counter from '../index';

describe('Counter组件', () => {
  test('初始值显示正确', () => {
    const { getByTestId } = render(Counter, {
      value: 5
    });
    
    expect(getByTestId('counter-value')).toHaveTextContent('5');
  });
  
  test('点击加号增加数值', async () => {
    const onChangeMock = jest.fn();
    const { getByTestId } = render(Counter, {
      value: 1,
      onChange: onChangeMock
    });
    
    await fireEvent.tap(getByTestId('counter-plus'));
    
    expect(onChangeMock).toHaveBeenCalledWith({
      detail: { value: 2 }
    });
  });
  
  test('达到最大值时禁用加号', () => {
    const { getByTestId } = render(Counter, {
      value: 10,
      max: 10
    });
    
    expect(getByTestId('counter-plus')).toHaveClass('disabled');
  });
});
```

### 组件快照测试

```typescript
// 快照测试确保组件结构稳定
test('Counter组件快照测试', () => {
  const component = render(Counter, {
    value: 3,
    min: 0,
    max: 10
  });
  
  expect(component).toMatchSnapshot();
});
```

## 组件文档生成

### 自动文档生成

```typescript
// 组件属性文档自动生成
interface ComponentDoc {
  name: string;
  description: string;
  properties: PropertyDoc[];
  events: EventDoc[];
  methods: MethodDoc[];
  examples: ExampleDoc[];
}

interface PropertyDoc {
  name: string;
  type: string;
  default: any;
  description: string;
  required: boolean;
}
```

## 性能优化

### 组件懒加载

```typescript
// 组件懒加载实现
class ComponentLazyLoader {
  private loadedComponents = new Set<string>();
  
  async loadComponent(componentName: string): Promise<void> {
    if (this.loadedComponents.has(componentName)) {
      return;
    }
    
    // 动态导入组件
    const component = await import(`../components_v2/${componentName}/index`);
    
    // 注册组件
    this.registerComponent(componentName, component.default);
    
    this.loadedComponents.add(componentName);
  }
}
```

### 组件缓存

```typescript
// 组件实例缓存
class ComponentCache {
  private cache = new Map<string, any>();
  
  getComponent(key: string): any {
    return this.cache.get(key);
  }
  
  setComponent(key: string, component: any): void {
    this.cache.set(key, component);
  }
  
  clearCache(): void {
    this.cache.clear();
  }
}
```

## 组件开发规范

### 命名规范

- **组件名称**：使用 kebab-case，如 `goods-item`
- **属性名称**：使用 camelCase，如 `showPrice`
- **事件名称**：使用 camelCase，如 `addToCart`
- **样式类名**：使用 BEM 规范，如 `goods-item__title--active`

### 代码规范

```typescript
// 组件模板
BaseComponent({
  // 1. 组件选项
  options: {
    multipleSlots: true
  },
  
  // 2. 外部样式类
  externalClasses: ['custom-class'],
  
  // 3. 属性定义
  properties: {
    // 属性定义
  },
  
  // 4. 数据定义
  data: {
    // 内部数据
  },
  
  // 5. 计算属性
  computed: {
    // 计算属性
  },
  
  // 6. 生命周期
  attached() {
    // 组件初始化
  },
  
  // 7. 方法定义
  methods: {
    // 组件方法
  }
});
```

### 文档规范

每个组件都应该包含：

1. **README.md**：组件说明文档
2. **属性说明**：详细的属性列表和说明
3. **事件说明**：组件触发的事件
4. **使用示例**：完整的使用示例
5. **样式说明**：可定制的样式类

## 组件库维护

### 版本管理

- **主版本号**：不兼容的API修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

### 更新日志

```markdown
## [1.2.0] - 2025-01-27

### Added
- 新增 `goods-skeleton` 骨架屏组件
- `counter` 组件支持小数步长

### Changed
- `dialog` 组件样式优化
- 优化组件加载性能

### Fixed
- 修复 `popup` 组件在某些机型上的显示问题
```

### 组件监控

- **使用统计**：统计各组件的使用频率
- **性能监控**：监控组件渲染性能
- **错误监控**：收集组件运行时错误
- **兼容性监控**：监控组件在不同平台的兼容性