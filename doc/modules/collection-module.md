<!-- collection-module.md -->
# 模块：收藏列表（collection）

> ⚠️ 本文件为自动生成骨架，内容待完善。

### 功能概述
展示用户收藏的商品或店铺，支持取消收藏、跳转详情。

### 核心文件
| 文件 | 职责 | 关键函数/组件 |
|------|------|--------------|
| src/pages/collection/index.ts | 收藏页逻辑 | fetchCollection, onUnCollect |

### 业务流程
```mermaid
graph LR
    A[页面加载] --> B[获取收藏列表]
    B --> C[展示收藏]
    C --> D[取消收藏]
```

### API 接口
| 接口 | 方法 | 用途 |
|------|------|------|
| /api/v1/collect/list | GET | 获取收藏列表 |
| /api/v1/collect/cancel | POST | 取消收藏 |
