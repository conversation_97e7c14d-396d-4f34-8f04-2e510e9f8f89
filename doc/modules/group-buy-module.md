<!-- group-buy-module.md -->
# 模块：团购页面（group-buy）

> ⚠️ 本文件为自动生成骨架，内容待完善。

### 功能概述
提供团购商品展示、拼团信息查看、参团/开团操作等功能。

### 核心文件
| 文件 | 职责 | 关键函数/组件 |
|------|------|--------------|
| src/pages/group-buy/index.ts | 团购页面主逻辑 | onLoad, joinGroup, createGroup |
| src/pages/group-buy/components/ | 团购子组件 | group-card, group-progress |

### 业务流程
```mermaid
graph LR
    A[页面加载] --> B[获取团购信息]
    B --> C[展示团购状态]
    C --> D[用户参团/开团]
    D --> E[下单跳转]
```

### 数据结构
```typescript
interface IGroupInfo {
  groupId: string;
  goods: IGoodsItem;
  memberCount: number;
  targetCount: number;
  expireTime: string;
}
```

### API 接口
| 接口 | 方法 | 用途 | 参数 |
|------|------|------|------|
| /api/v1/group/info | GET | 获取团购信息 | groupId |
| /api/v1/group/join | POST | 参团 | groupId |
| /api/v1/group/create | POST | 开团 | goodsId |

### 依赖关系
- 依赖模块：cart, user
- 被依赖于：order

