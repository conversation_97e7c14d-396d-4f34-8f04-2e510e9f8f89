<!-- user-module.md -->
# 模块：用户系统

### 功能概述
用户系统负责用户授权、地址管理、隐私协议、用户信息等功能。

### 核心文件
| 文件 | 职责 | 关键函数/组件 |
|------|------|--------------|
| src/pages/address/index.ts | 地址管理页 | fetchAddress, addAddress |
| src/utils/userSwitch.ts | 用户切换 | onSwitchLogin, checkSession |
| src/utils/business.ts | 业务工具 | getUserInfo, checkAuth |
| src/behaviors/businessCheckBehavior.ts | 业务检查行为 | 权限检查、隐私授权 |

### 业务流程
```mermaid
graph LR
    A[用户授权] --> B[获取用户信息]
    B --> C[地址管理]
    C --> D[隐私协议]
    D --> E[业务操作]
```

### 数据结构
```typescript
interface IUser {
  userId: string;
  nickname: string;
  avatar: string;
  phone: string;
}

interface IAddress {
  addressId: string;
  name: string;
  phone: string;
  address: string;
  isDefault: boolean;
}
```

### API 接口
| 接口 | 方法 | 用途 | 参数 |
|------|------|------|------|
| /api/v1/user/info | GET | 获取用户信息 | - |
| /api/v1/address/list | GET | 获取地址列表 | - |
| /api/v1/address/add | POST | 添加地址 | address |

### 关键业务逻辑
- 用户授权：微信/支付宝授权登录
- 地址管理：添加、编辑、删除、设置默认地址
- 隐私协议：隐私协议授权状态管理

### 依赖关系
- 依赖模块：utils
- 被依赖于：home, order, store

