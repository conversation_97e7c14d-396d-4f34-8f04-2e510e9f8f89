<!-- payment-module.md -->
# 模块：支付系统

### 功能概述
支付系统负责支付方式选择、支付流程、储值卡支付、支付结果处理等功能。

### 核心文件
| 文件 | 职责 | 关键函数/组件 |
|------|------|--------------|
| src/pages/pay/index.ts | 支付页面 | selectPayWay, onPay |
| src/pages/pay-csb/index.ts | 储值卡支付页 | payByStoredCard, checkBalance |
| src/plugins/paywayPlugin/ | 支付方式插件 | 支付方式选择逻辑 |
| src/plugins/payByStoredCardPlugin/ | 储值卡支付插件 | 储值卡支付逻辑 |

### 业务流程
```mermaid
graph LR
    A[选择支付方式] --> B[发起支付]
    B --> C[支付处理]
    C --> D[支付结果]
    D --> E[订单状态更新]
```

### 数据结构
```typescript
interface IPayment {
  payWay: number;
  amount: number;
  orderSn: string;
  payResult: IPayResult;
}
```

### API 接口
| 接口 | 方法 | 用途 | 参数 |
|------|------|------|------|
| /api/v1/pay/create | POST | 创建支付 | orderSn, payWay |
| /api/v1/pay/result | GET | 查询支付结果 | payId |
| /api/v1/stored-card/pay | POST | 储值卡支付 | orderSn, cardId |

### 关键业务逻辑
- 支付方式：微信支付、支付宝支付、储值卡支付
- 支付流程：创建支付、调起支付、处理结果
- 异常处理：支付失败、网络异常等情况处理

### 依赖关系
- 依赖模块：order, user, utils
- 被依赖于：home, order
