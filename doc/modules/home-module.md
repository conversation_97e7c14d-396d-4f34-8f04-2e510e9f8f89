# 首页点餐模块详细文档

## 模块概述

首页点餐模块是 Smart MP Biz 的核心模块，负责商品展示、购物车管理、服务类型切换、优惠活动等核心业务功能。该模块采用插件化架构，支持多种服务类型和营销活动。

## 技术架构

### 核心组件结构

```
src/pages/home/
├── index.ts                    # 主页面逻辑
├── index.wxml                  # 页面模板
├── index.less                  # 页面样式
├── utils/
│   ├── HooksManager.ts         # 插件管理器
│   ├── Methods.ts              # 业务方法集合
│   ├── Process.ts              # 业务流程处理
│   └── GoodsScroller.ts        # 商品滚动控制
├── plugins/                    # 业务插件
├── template/                   # 模板文件
└── styles/                     # 样式文件
```

### 插件系统

首页采用插件化架构，主要插件包括：

| 插件名 | 功能 | 触发时机 |
|--------|------|----------|
| loadCartPlugin | 加载购物车数据 | 页面初始化 |
| loadMerchantPlugin | 加载商户信息 | 页面初始化 |
| handleDiscountLogicPlugin | 处理优惠逻辑 | 商品加购后 |
| handleSettlementPlugin | 处理结算逻辑 | 点击结算按钮 |
| updateBadgePlugin | 更新徽章数量 | 购物车变更 |

## 核心业务流程

### 页面初始化流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Page as 首页
    participant Store as 商店服务
    participant Cart as 购物车
    participant Goods as 商品服务
    
    User->>Page: 进入首页
    Page->>Store: 获取店铺信息
    Store-->>Page: 返回店铺数据
    Page->>Goods: 获取商品列表
    Goods-->>Page: 返回商品数据
    Page->>Cart: 初始化购物车
    Cart-->>Page: 返回购物车数据
    Page->>User: 渲染页面
```

### 商品加购流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Page as 首页
    participant Cart as 购物车
    participant API as 后端API
    
    User->>Page: 点击加购按钮
    Page->>Cart: 添加商品到购物车
    Cart->>Cart: 本地计算优惠
    Cart->>Page: 更新UI显示
    Cart->>API: 同步购物车数据
    API-->>Cart: 返回服务端计算结果
    Cart->>Page: 更新最终数据
```

## 关键方法说明

### onAddToCart - 商品加购

```typescript
async onAddToCart(e) {
  const { goodsId, num, attachInfo } = getDataset(e);
  
  // 1. 参数验证
  if (!goodsId || num <= 0) return;
  
  // 2. 库存检查
  const goods = this.getGoodsById(goodsId);
  if (!this.checkStock(goods, num)) {
    showToast('库存不足');
    return;
  }
  
  // 3. 添加到购物车
  await CartStore.getInstance().addToCart({
    goodsId,
    num,
    attachInfo
  });
  
  // 4. 更新UI
  this.updateGoodsNum(goodsId, num);
}
```

### onSubmit - 提交订单

```typescript
async onSubmit() {
  // 1. 购物车验证
  const cart = CartStore.getInstance().getCart();
  if (!cart.records.length) {
    showToast('购物车为空');
    return;
  }
  
  // 2. 业务校验
  const canSubmit = await canSubmitOrder(cart);
  if (!canSubmit.success) {
    showToast(canSubmit.message);
    return;
  }
  
  // 3. 跳转到订单确认页
  this.bridge.navigateTo({
    path: '/pages/submit/index',
    query: { from: 'home' }
  });
}
```

## 数据结构定义

### 页面数据结构

```typescript
interface HomePageData {
  // 商品相关
  goods: IGoodsCategory[];
  selectedCategory: string;
  searchKeyword: string;
  
  // 购物车相关
  cart: ICart;
  cartTotal: number;
  cartCount: number;
  
  // 店铺相关
  store: IStore;
  mealType: string;
  serviceType: string;
  
  // UI状态
  loading: boolean;
  refreshing: boolean;
  showCart: boolean;
  
  // 其他
  config: IThemeConfig;
  extra: IExtra;
}
```

### 商品数据结构

```typescript
interface IGoodsCategory {
  categoryId: string;
  categoryName: string;
  items: IGoodsItem[];
}

interface IGoodsItem {
  goodsId: string;
  goodsName: string;
  price: number;
  originalPrice: number;
  image: string;
  description: string;
  stock: number;
  num: number; // 购物车中的数量
  isRecommend: boolean;
  tags: string[];
  attachInfo?: IAttachInfo;
}
```

## 性能优化策略

### 1. 数据缓存

- **商品数据缓存**：相同服务类型下的商品数据缓存30分钟
- **店铺信息缓存**：店铺基础信息缓存1小时
- **购物车缓存**：本地存储购物车数据，支持离线操作

### 2. 渲染优化

- **虚拟滚动**：商品列表采用虚拟滚动，减少DOM节点
- **图片懒加载**：商品图片采用懒加载策略
- **防抖节流**：加购操作采用防抖，滚动事件采用节流

### 3. 网络优化

- **请求合并**：相同时间窗口内的相似请求进行合并
- **预加载**：预加载下一页商品数据
- **离线支持**：支持离线浏览和操作

## 错误处理机制

### 1. 网络错误

```typescript
// 网络请求失败重试机制
async function retryRequest(fn, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await sleep(1000 * (i + 1)); // 指数退避
    }
  }
}
```

### 2. 业务错误

- **库存不足**：显示库存不足提示，禁用加购按钮
- **商品下架**：灰显商品，显示"已售完"标识
- **服务异常**：显示友好的错误提示，提供重试选项

### 3. 兜底策略

- **数据异常**：使用缓存数据或默认数据
- **组件异常**：降级到基础组件
- **功能异常**：隐藏异常功能，保证核心流程可用

## 测试策略

### 单元测试

```typescript
describe('首页加购功能', () => {
  test('正常加购流程', async () => {
    const page = new HomePage();
    const result = await page.onAddToCart({
      detail: { goodsId: '123', num: 1 }
    });
    expect(result.success).toBe(true);
  });
  
  test('库存不足处理', async () => {
    const page = new HomePage();
    const result = await page.onAddToCart({
      detail: { goodsId: '456', num: 999 }
    });
    expect(result.success).toBe(false);
    expect(result.message).toBe('库存不足');
  });
});
```

### 集成测试

- **页面渲染测试**：验证页面正常渲染
- **交互功能测试**：验证加购、切换服务类型等功能
- **数据同步测试**：验证购物车数据同步

## 监控和埋点

### 性能监控

- **页面加载时间**：监控首页加载性能
- **接口响应时间**：监控关键接口性能
- **内存使用**：监控内存泄漏

### 业务埋点

- **商品曝光**：记录商品展示次数
- **加购行为**：记录用户加购行为
- **转化漏斗**：记录从浏览到下单的转化路径

## 扩展指南

### 添加新的服务类型

1. 在 `mealType` 枚举中添加新类型
2. 在 `setDataMealType` 方法中添加处理逻辑
3. 更新商品数据获取逻辑
4. 添加对应的UI展示

### 添加新的营销活动

1. 创建新的插件文件
2. 在 `HooksManager` 中注册插件
3. 在合适的时机调用插件
4. 更新优惠计算逻辑

### 添加新的商品类型

1. 扩展 `IGoodsItem` 接口
2. 更新商品组件
3. 添加对应的业务逻辑
4. 更新购物车处理逻辑