<!-- store-module.md -->
# 模块：商店管理系统

### 功能概述
商店管理模块负责店铺信息展示、店铺列表、收藏管理、店铺详情等功能。

### 核心文件
| 文件 | 职责 | 关键函数/组件 |
|------|------|--------------|
| src/pages/stores/index.js | 店铺列表页 | fetchStores, onSearch |
| src/pages/store-home/index.ts | 店铺首页 | fetchStoreDetail, onCollect |
| src/components_v2/store-basic/ | 店铺基础信息组件 | 展示店铺名称、地址、评分 |
| src/components_v2/store-collection/ | 店铺收藏组件 | 收藏/取消收藏 |

### 业务流程
```mermaid
graph LR
    A[店铺列表] --> B[选择店铺]
    B --> C[店铺详情]
    C --> D[收藏店铺]
    D --> E[进入点餐]
```

### 数据结构
```typescript
interface IStore {
  storeId: string;
  storeName: string;
  storeAddress: IStoreAddress;
  location: ILocation;
  payWay: number;
  businessHours: string;
}
```

### API 接口
| 接口 | 方法 | 用途 | 参数 |
|------|------|------|------|
| /api/v1/stores | GET | 获取店铺列表 | lat, lon, keyword |
| /api/v1/store/detail | GET | 获取店铺详情 | storeId |
| /api/v1/store/collect | POST | 收藏店铺 | storeId |

### 关键业务逻辑
- 店铺搜索：支持按距离、关键词搜索店铺
- 收藏管理：用户可以收藏/取消收藏店铺
- 营业状态：显示店铺营业时间和当前状态

### 依赖关系
- 依赖模块：utils, user
- 被依赖于：home, order

