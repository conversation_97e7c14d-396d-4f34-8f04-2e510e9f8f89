# 订单模块详细文档

## 模块概述

订单模块负责订单的完整生命周期管理，包括订单创建、支付、状态跟踪、详情展示、退款申请等功能。支持多种订单类型（普通订单、围餐订单、团购订单）和多种支付方式。

## 技术架构

### 核心页面结构

```
src/pages/
├── submit/                     # 订单提交页
│   ├── index.ts               # 主逻辑
│   ├── SubmitManager.ts       # 提交管理器
│   └── components/            # 子组件
├── my-orders/                 # 我的订单页
├── order-detail/              # 订单详情页
│   ├── lib/actions.ts         # 订单操作
│   └── dialogs/               # 弹窗组件
├── refund-submit/             # 退款申请页
├── refund-detail/             # 退款详情页
└── pay/                       # 支付页面
```

### 订单状态流转

```mermaid
stateDiagram-v2
    [*] --> 待支付: 创建订单
    待支付 --> 已支付: 支付成功
    待支付 --> 已取消: 支付超时/用户取消
    已支付 --> 制作中: 商家接单
    制作中 --> 配送中: 开始配送
    制作中 --> 待取餐: 堂食/自取
    配送中 --> 已完成: 配送完成
    待取餐 --> 已完成: 用户取餐
    已支付 --> 退款中: 申请退款
    退款中 --> 已退款: 退款成功
    退款中 --> 已支付: 退款失败
```

## 核心功能实现

### 1. 订单提交流程

```typescript
class SubmitManager {
  async submitOrder(): Promise<ISubmitResult> {
    try {
      // 1. 订单验证
      await this.validateOrder();
      
      // 2. 地址验证
      await this.validateAddress();
      
      // 3. 支付方式验证
      await this.validatePayment();
      
      // 4. 创建订单
      const order = await this.createOrder();
      
      // 5. 发起支付
      const payResult = await this.processPayment(order);
      
      // 6. 处理支付结果
      return await this.handlePayResult(payResult);
      
    } catch (error) {
      this.handleSubmitError(error);
      throw error;
    }
  }
  
  private async validateOrder(): Promise<void> {
    const cart = CartStore.getInstance().getCart();
    
    // 购物车不能为空
    if (!cart.records.length) {
      throw new Error('购物车为空');
    }
    
    // 检查商品状态
    for (const record of cart.records) {
      await this.validateGoodsStatus(record);
    }
    
    // 检查起送价
    await this.validateMinDeliveryPrice(cart);
  }
}
```

### 2. 订单详情展示

```typescript
interface OrderDetailPage {
  async fetchOrderDetail(orderSn: string): Promise<IOrderDetail> {
    try {
      const response = await OrderService.getOrderDetail({ orderSn });
      const orderDetail = this.processOrderDetail(response.data);
      
      // 更新页面数据
      this.setData({ orderDetail });
      
      // 启动状态轮询
      this.startStatusPolling(orderSn);
      
      return orderDetail;
    } catch (error) {
      this.handleFetchError(error);
      throw error;
    }
  }
  
  private startStatusPolling(orderSn: string): void {
    // 只有进行中的订单才需要轮询
    if (!this.needPolling()) return;
    
    this.pollingTimer = setInterval(async () => {
      try {
        const status = await OrderService.getOrderStatus({ orderSn });
        this.updateOrderStatus(status);
        
        // 订单完成后停止轮询
        if (this.isOrderFinished(status)) {
          this.stopStatusPolling();
        }
      } catch (error) {
        console.error('订单状态轮询失败', error);
      }
    }, 10000); // 10秒轮询一次
  }
}
```

### 3. 退款申请流程

```typescript
class RefundManager {
  async submitRefund(params: IRefundParams): Promise<IRefundResult> {
    try {
      // 1. 退款参数验证
      this.validateRefundParams(params);
      
      // 2. 计算退款金额
      const refundAmount = await this.calculateRefundAmount(params);
      
      // 3. 提交退款申请
      const refundResult = await OrderService.submitRefund({
        ...params,
        refundAmount
      });
      
      // 4. 处理退款结果
      return this.processRefundResult(refundResult);
      
    } catch (error) {
      this.handleRefundError(error);
      throw error;
    }
  }
  
  private async calculateRefundAmount(params: IRefundParams): Promise<number> {
    const { orderSn, refundGoods } = params;
    
    // 获取订单详情
    const order = await OrderService.getOrderDetail({ orderSn });
    
    // 计算商品退款金额
    let goodsRefundAmount = 0;
    for (const refundGood of refundGoods) {
      const orderGood = order.goods.find(g => g.goodsId === refundGood.goodsId);
      goodsRefundAmount += orderGood.price * refundGood.refundNum;
    }
    
    // 计算优惠分摊
    const discountRefund = this.calculateDiscountRefund(order, goodsRefundAmount);
    
    // 计算配送费退款
    const deliveryRefund = this.calculateDeliveryRefund(order, refundGoods);
    
    return goodsRefundAmount - discountRefund + deliveryRefund;
  }
}
```

## 数据结构设计

### 订单数据结构

```typescript
interface IOrder {
  // 基础信息
  orderSn: string;              // 订单号
  storeId: string;              // 店铺ID
  storeName: string;            // 店铺名称
  
  // 状态信息
  status: OrderStatus;          // 订单状态
  payStatus: PayStatus;         // 支付状态
  refundStatus?: RefundStatus;  // 退款状态
  
  // 金额信息
  totalPrice: number;           // 订单总价
  goodsPrice: number;           // 商品总价
  deliveryFee: number;          // 配送费
  discountAmount: number;       // 优惠金额
  actualPrice: number;          // 实付金额
  
  // 商品信息
  goods: IOrderGoods[];         // 订单商品
  
  // 地址信息
  address?: IOrderAddress;      // 配送地址
  
  // 支付信息
  payWay: PayWay;              // 支付方式
  payTime?: string;            // 支付时间
  
  // 时间信息
  createTime: string;          // 创建时间
  expectTime?: string;         // 期望送达时间
  finishTime?: string;         // 完成时间
  
  // 其他信息
  remark?: string;             // 订单备注
  mealType: MealType;          // 服务类型
  extra?: IOrderExtra;         // 扩展信息
}

interface IOrderGoods {
  goodsId: string;
  goodsName: string;
  price: number;
  num: number;
  totalPrice: number;
  image: string;
  specs?: ISpec[];
  refundNum?: number;          // 已退款数量
}
```

### 退款数据结构

```typescript
interface IRefund {
  refundSn: string;            // 退款单号
  orderSn: string;             // 原订单号
  refundAmount: number;        // 退款金额
  refundReason: string;        // 退款原因
  refundGoods: IRefundGoods[]; // 退款商品
  status: RefundStatus;        // 退款状态
  createTime: string;          // 申请时间
  processTime?: string;        // 处理时间
}

interface IRefundGoods {
  goodsId: string;
  goodsName: string;
  refundNum: number;
  refundPrice: number;
  reason: string;
}
```

## 核心业务逻辑

### 1. 订单验证规则

```typescript
class OrderValidator {
  // 起送价验证
  validateMinDeliveryPrice(cart: ICart, store: IStore): boolean {
    if (store.minDeliveryPrice && cart.totalPrice < store.minDeliveryPrice) {
      throw new Error(`未达到起送价 ¥${store.minDeliveryPrice}`);
    }
    return true;
  }
  
  // 配送范围验证
  validateDeliveryRange(address: IAddress, store: IStore): boolean {
    const distance = calculateDistance(address.location, store.location);
    if (distance > store.maxDeliveryDistance) {
      throw new Error('超出配送范围');
    }
    return true;
  }
  
  // 营业时间验证
  validateBusinessHours(store: IStore): boolean {
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    if (currentTime < store.businessStart || currentTime > store.businessEnd) {
      throw new Error('不在营业时间内');
    }
    return true;
  }
  
  // 库存验证
  async validateStock(cart: ICart): Promise<boolean> {
    for (const record of cart.records) {
      const stock = await GoodsService.getStock({ goodsId: record.goodsId });
      if (stock < record.num) {
        throw new Error(`${record.goodsName} 库存不足`);
      }
    }
    return true;
  }
}
```

### 2. 支付流程处理

```typescript
class PaymentProcessor {
  async processPayment(order: IOrder): Promise<IPayResult> {
    const { payWay, actualPrice, orderSn } = order;
    
    switch (payWay) {
      case PayWay.WECHAT:
        return await this.processWechatPay(order);
        
      case PayWay.ALIPAY:
        return await this.processAlipayPay(order);
        
      case PayWay.STORED_CARD:
        return await this.processStoredCardPay(order);
        
      default:
        throw new Error('不支持的支付方式');
    }
  }
  
  private async processWechatPay(order: IOrder): Promise<IPayResult> {
    // 1. 创建微信支付订单
    const payOrder = await PaymentService.createWechatPay({
      orderSn: order.orderSn,
      amount: order.actualPrice,
      description: `${order.storeName}-订单支付`
    });
    
    // 2. 调起微信支付
    const payResult = await wx.requestPayment({
      timeStamp: payOrder.timeStamp,
      nonceStr: payOrder.nonceStr,
      package: payOrder.package,
      signType: payOrder.signType,
      paySign: payOrder.paySign
    });
    
    // 3. 验证支付结果
    return await this.verifyPayResult(order.orderSn, payResult);
  }
}
```

### 3. 订单状态管理

```typescript
class OrderStatusManager {
  // 获取订单状态文本
  getStatusText(status: OrderStatus): string {
    const statusMap = {
      [OrderStatus.PENDING_PAYMENT]: '待支付',
      [OrderStatus.PAID]: '已支付',
      [OrderStatus.PREPARING]: '制作中',
      [OrderStatus.DELIVERING]: '配送中',
      [OrderStatus.COMPLETED]: '已完成',
      [OrderStatus.CANCELLED]: '已取消'
    };
    return statusMap[status] || '未知状态';
  }
  
  // 获取可执行的操作
  getAvailableActions(order: IOrder): OrderAction[] {
    const actions: OrderAction[] = [];
    
    switch (order.status) {
      case OrderStatus.PENDING_PAYMENT:
        actions.push(OrderAction.PAY, OrderAction.CANCEL);
        break;
        
      case OrderStatus.PAID:
      case OrderStatus.PREPARING:
        actions.push(OrderAction.REFUND);
        break;
        
      case OrderStatus.DELIVERING:
        actions.push(OrderAction.TRACK, OrderAction.CONTACT_RIDER);
        break;
        
      case OrderStatus.COMPLETED:
        actions.push(OrderAction.REORDER, OrderAction.EVALUATE);
        break;
    }
    
    return actions;
  }
  
  // 检查是否可以退款
  canRefund(order: IOrder): boolean {
    // 已支付且未完成的订单可以退款
    return [OrderStatus.PAID, OrderStatus.PREPARING].includes(order.status);
  }
}
```

## API 接口设计

### 订单相关接口

```typescript
interface OrderAPI {
  // 创建订单
  createOrder(params: ICreateOrderParams): Promise<IOrder>;
  
  // 获取订单列表
  getOrderList(params: IOrderListParams): Promise<IOrderListResult>;
  
  // 获取订单详情
  getOrderDetail(params: { orderSn: string }): Promise<IOrder>;
  
  // 取消订单
  cancelOrder(params: { orderSn: string; reason: string }): Promise<void>;
  
  // 申请退款
  submitRefund(params: IRefundParams): Promise<IRefund>;
  
  // 获取退款详情
  getRefundDetail(params: { refundSn: string }): Promise<IRefund>;
}

interface ICreateOrderParams {
  storeId: string;
  goods: IOrderGoods[];
  address?: IAddress;
  payWay: PayWay;
  mealType: MealType;
  remark?: string;
  expectTime?: string;
}
```

### 支付相关接口

```typescript
interface PaymentAPI {
  // 创建支付订单
  createPayment(params: ICreatePaymentParams): Promise<IPaymentOrder>;
  
  // 查询支付结果
  queryPayResult(params: { payId: string }): Promise<IPayResult>;
  
  // 储值卡支付
  payByStoredCard(params: IStoredCardPayParams): Promise<IPayResult>;
}
```

## 错误处理策略

### 订单提交错误

```typescript
enum OrderErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',    // 验证错误
  STOCK_ERROR = 'STOCK_ERROR',              // 库存错误
  PAYMENT_ERROR = 'PAYMENT_ERROR',          // 支付错误
  NETWORK_ERROR = 'NETWORK_ERROR',          // 网络错误
  BUSINESS_ERROR = 'BUSINESS_ERROR'         // 业务错误
}

class OrderErrorHandler {
  handleSubmitError(error: OrderError): void {
    switch (error.type) {
      case OrderErrorType.VALIDATION_ERROR:
        // 验证错误：显示具体错误信息
        showToast(error.message);
        break;
        
      case OrderErrorType.STOCK_ERROR:
        // 库存错误：更新商品状态，提示用户
        this.handleStockError(error.data);
        break;
        
      case OrderErrorType.PAYMENT_ERROR:
        // 支付错误：跳转到支付页面重试
        this.redirectToPayment(error.data.orderSn);
        break;
        
      case OrderErrorType.NETWORK_ERROR:
        // 网络错误：显示重试按钮
        this.showRetryDialog();
        break;
        
      default:
        // 其他错误：显示通用错误提示
        showToast('订单提交失败，请稍后重试');
    }
  }
}
```

## 性能优化

### 1. 订单列表优化

```typescript
class OrderListOptimizer {
  // 虚拟滚动
  implementVirtualScroll(): void {
    // 只渲染可见区域的订单
    const visibleOrders = this.getVisibleOrders();
    this.setData({ visibleOrders });
  }
  
  // 图片懒加载
  implementImageLazyLoad(): void {
    // 订单商品图片懒加载
    this.intersectionObserver = wx.createIntersectionObserver();
    this.intersectionObserver.observe('.order-goods-image', (res) => {
      if (res.intersectionRatio > 0) {
        this.loadImage(res.target);
      }
    });
  }
  
  // 分页加载
  async loadMoreOrders(): Promise<void> {
    if (this.loading || !this.hasMore) return;
    
    this.loading = true;
    try {
      const newOrders = await OrderService.getOrderList({
        page: this.currentPage + 1,
        limit: 10
      });
      
      this.setData({
        orders: [...this.data.orders, ...newOrders.list],
        hasMore: newOrders.hasMore
      });
      
      this.currentPage++;
    } finally {
      this.loading = false;
    }
  }
}
```

### 2. 缓存策略

```typescript
class OrderCache {
  private cache = new Map<string, IOrder>();
  private cacheExpiry = new Map<string, number>();
  
  // 缓存订单详情
  cacheOrderDetail(orderSn: string, order: IOrder): void {
    this.cache.set(orderSn, order);
    this.cacheExpiry.set(orderSn, Date.now() + 5 * 60 * 1000); // 5分钟过期
  }
  
  // 获取缓存的订单详情
  getCachedOrderDetail(orderSn: string): IOrder | null {
    const expiry = this.cacheExpiry.get(orderSn);
    if (!expiry || Date.now() > expiry) {
      this.cache.delete(orderSn);
      this.cacheExpiry.delete(orderSn);
      return null;
    }
    
    return this.cache.get(orderSn) || null;
  }
}
```

## 监控和埋点

### 业务埋点

```typescript
class OrderAnalytics {
  // 订单提交埋点
  trackOrderSubmit(order: IOrder): void {
    this.track('order_submit', {
      order_sn: order.orderSn,
      store_id: order.storeId,
      total_price: order.totalPrice,
      goods_count: order.goods.length,
      pay_way: order.payWay,
      meal_type: order.mealType
    });
  }
  
  // 支付成功埋点
  trackPaymentSuccess(order: IOrder, payTime: number): void {
    this.track('payment_success', {
      order_sn: order.orderSn,
      pay_way: order.payWay,
      amount: order.actualPrice,
      pay_duration: payTime
    });
  }
  
  // 退款申请埋点
  trackRefundSubmit(refund: IRefund): void {
    this.track('refund_submit', {
      order_sn: refund.orderSn,
      refund_amount: refund.refundAmount,
      refund_reason: refund.refundReason,
      goods_count: refund.refundGoods.length
    });
  }
}
```

### 性能监控

- **订单提交耗时**：从点击提交到订单创建成功的时间
- **支付成功率**：支付操作的成功率
- **页面加载时间**：订单详情页的加载时间
- **接口响应时间**：订单相关接口的响应时间