# 后付围餐支付完成后隐藏支付按钮技术方案

**文档创建日期：** 2025年8月4日  
**需求来源：** https://sqb.feishu.cn/wiki/ZqK5wIJSXiQ244kCnGRc6novn4b  
**影响文件：** 
- `src/pages/order-detail/lib/actions.ts`

---

## 1. 背景

**产品方案URL:** https://sqb.feishu.cn/wiki/ZqK5wIJSXiQ244kCnGRc6novn4b

根据产品需求，当后付围餐订单支付完成后（状态为203），订单详情页不应该继续显示"去支付"按钮，避免用户重复支付的困扰。

## 2. 目标

当订单满足以下条件时，隐藏"去支付"按钮：
- 订单状态 `status` 为 203（已支付并接受）
- 订单类型为后付围餐（`EAT_FIRST_ORDER`）

## 3. 解决方案

### 3.1. 代码分析结果

通过对相关代码的详细分析，发现问题的根源在于订单详情页的支付按钮显示逻辑：

**当前支付按钮显示逻辑**
位置：`src/pages/order-detail/lib/actions.ts` (第343-346行)

```typescript
const shouldShowPay =
  (_.includes(WAIT_PAYED_STATUS, status) && type !== PAY_FIRST_TABLE_ORDER) ||
  (type === EAT_FIRST_ORDER && _.includes(['INIT', 'ACCEPTED'], process_status)) ||
  (type === PAY_FIRST_TABLE_ORDER && pay_controll && pay_controll.allow_pay)
```

#### 3.2 问题所在
当前逻辑中，对于后付围餐（`EAT_FIRST_ORDER`）类型的订单，只要 `process_status` 为 `'INIT'` 或 `'ACCEPTED'`，就会显示支付按钮。
   
当 `type === EAT_FIRST_ORDER` 时：
- 手动结账配置下，支付成功后，`process_status` 为 `'ACCEPTED'`
- 自动结账配置下，支付成功后，`process_status` 为 `'COMPLETED'`


### 3.2 最终实现方案
可以在去支付按钮是否展示的判断逻辑中`(type === EAT_FIRST_ORDER && _.includes(['INIT', 'ACCEPTED'], process_status))`，增加一个对 `203` 状态的处理。即在手动结账情况下，后付围餐支付成功后不展示去支付按钮。

**确保正确导入CONFIG常量: PAID_AND_ACCEPTED_STATUS**
```typescript
const shouldShowPay =
  (_.includes(WAIT_PAYED_STATUS, status) && type !== PAY_FIRST_TABLE_ORDER) ||
  (type === EAT_FIRST_ORDER && _.includes(['INIT', 'ACCEPTED'], process_status) && status !== PAID_AND_ACCEPTED_STATUS) ||
  (type === PAY_FIRST_TABLE_ORDER && pay_controll && pay_controll.allow_pay)
```
- 203不在 `WAIT_PAYED_STATUS` 中，第一个条件为 `false`


### 3.3 实际修改内容

#### 唯一修改文件：`src/pages/order-detail/lib/actions.ts`

**修改位置：** 第9-27行的CONFIG导入部分，确保导入 `PAID_AND_ACCEPTED_STATUS`

**原有代码（已满足需求）：**
```typescript
when(item) {
  const shouldShowPay =
    (_.includes(WAIT_PAYED_STATUS, status) && type !== PAY_FIRST_TABLE_ORDER) ||
    (type === EAT_FIRST_ORDER && _.includes(['INIT', 'ACCEPTED'], process_status) && status !== PAID_AND_ACCEPTED_STATUS) ||
    (type === PAY_FIRST_TABLE_ORDER && pay_controll && pay_controll.allow_pay)

  if (shouldShowPay) {
    _.assign(item, { visible: true, highlight: true })
  }

  return shouldShowPay
}
```

**依赖关系分析：**
- 订单详情页第14行：`import { getActionsByOrder } from './lib/actions'`
- 订单详情页第794行：调用 `getActionsByOrder` 获取按钮列表
- `src/utils/order_detail_action.ts` 仅用于订单状态文案渲染，不影响按钮显示逻辑


