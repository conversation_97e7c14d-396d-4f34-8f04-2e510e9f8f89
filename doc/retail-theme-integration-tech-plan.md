# 零售门店主题接入技术方案

## 1. 需求背景

https://jira.wosai-inc.com/browse/SMART-27050
零售门店需要支持主题定制功能，让不同的门店可以根据品牌特色配置不同的主题色彩，提升品牌一致性和用户体验。

## 2. 技术目标

- 将零售相关页面的硬编码颜色替换为主题变量
- 支持动态主题切换
- 保持向下兼容性
- 统一主题色应用规范

## 3. 技术方案

### 3.1 主题变量定义

使用CSS自定义属性（CSS Variables）来定义主题色：

```css
:root {
  --primary__color: #ff6a16;           /* 主题色 */
  --primary-selected__color: rgba(255, 202, 95, 0.19); /* 主题选中背景色 */
}
```

### 3.2 颜色替换策略

#### 3.2.1 主题变量使用
```css
/* 写法 - 确保主题变量已定义时使用 */
color: var(--primary__color);
```

#### 3.2.2 替换范围

**列表模式样式** (`goods-list.less`)
- `.active-sub-category` 选中状态的文字颜色和边框色
- 选中背景色使用专用的主题选中背景变量

**网格模式样式** (`category-grid.less`, `goods-grid.less`)
- 分类选中状态的文字颜色
- 商品网格选中状态的文字颜色

### 3.3 实现细节

#### 3.3.1 样式文件修改

1. **goods-list.less** - 零售商品列表样式
   - 修改二级分类选中状态样式
   - 使用主题变量替换硬编码的橙色

2. **category-grid.less** - 分类网格样式
   - 修改分类选中状态文字颜色
   - 从黑色改为主题色

3. **goods-grid.less** - 商品网格样式
   - 修改商品分类选中状态文字颜色
   - 从深灰色改为主题色

#### 3.3.2 主题变量管理

```less
// 使用示例
.active-sub-category {
  color: var(--primary__color);
  border: 1px solid var(--primary__color);
  background: var(--primary-selected__color);
}
```

## 4. 技术优势

### 4.1 灵活性
- 支持运行时动态切换主题
- 可通过JavaScript修改CSS变量实现主题切换

### 4.2 维护性
- 集中管理主题色彩
- 减少重复的硬编码颜色值
- 便于后续主题扩展

## 5. 风险评估

### 5.1 样式冲突风险
**风险**: 主题变量可能与现有样式产生冲突
**解决方案**: 
- 使用明确的变量命名规范
- 充分测试不同主题配置

### 5.2 性能风险
**风险**: CSS变量可能影响渲染性能
**解决方案**: 
- 合理使用变量，避免过度嵌套
- 在根元素定义变量，减少查找开销

## 6. 测试策略

### 6.1 功能测试
- 验证不同主题色下的显示效果
- 测试主题切换功能

### 6.2 兼容性测试
- 测试主流浏览器兼容性
- 验证移动端显示效果
- 测试小程序环境兼容性

### 6.3 性能测试
- 测试主题切换的响应速度
- 验证CSS变量对渲染性能的影响

## 7. 部署计划

### 7.1 分阶段部署
1. **阶段一**: 完成样式文件修改
2. **阶段二**: 验证主题变量定义和应用
3. **阶段三**: 测试和优化主题切换功能

### 7.2 回滚策略
- 保留原有硬编码颜色作为fallback
- 可通过配置快速回滚到原有样式
- 监控线上异常，及时响应

## 8. 后续扩展

### 8.1 主题配置化
- 支持通过配置文件定义多套主题
- 实现主题预设和自定义主题

### 8.2 更多组件支持
- 扩展到更多页面和组件
- 建立完整的主题系统